USE [restaurant_db]
GO
/****** Object:  Table [dbo].[categories]    Script Date: 28/05/2025 05:17:40 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[categories](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](100) NOT NULL,
	[is_drink] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[food_ingredients]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[food_ingredients](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[food_id] [int] NULL,
	[ingredient_id] [int] NULL,
	[amount] [decimal](10, 2) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[foods]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[foods](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](255) NULL,
	[price] [decimal](10, 2) NOT NULL,
	[image_url] [nvarchar](max) NULL,
	[category_id] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[ingredients]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[ingredients](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](100) NOT NULL,
	[unit] [nvarchar](20) NOT NULL,
	[quantity] [decimal](10, 2) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[kitchen_queue]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[kitchen_queue](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[order_detail_id] [int] NULL,
	[status] [nvarchar](30) NULL,
	[updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[logs]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[logs](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[user_id] [int] NULL,
	[action] [nvarchar](255) NULL,
	[created_at] [datetime] NULL,
	[table_id] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[order_details]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[order_details](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[order_id] [int] NULL,
	[food_id] [int] NULL,
	[quantity] [int] NOT NULL,
	[price] [decimal](10, 2) NOT NULL,
	[custom_name] [nvarchar](255) NULL,
	[is_free_drink] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[orders]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[orders](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[table_id] [int] NULL,
	[user_id] [int] NULL,
	[order_time] [datetime] NULL,
	[status] [nvarchar](20) NULL,
	[total] [decimal](18, 2) NULL,
	[is_buffet] [bit] NOT NULL,
	[table_key] [nvarchar](100) NULL,
	[buffet_session_id] [nvarchar](100) NULL,
	[payment_status] [nvarchar](50) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[payments]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[payments](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[order_id] [int] NOT NULL,
	[payment_method] [nvarchar](50) NOT NULL,
	[amount] [decimal](10, 2) NOT NULL,
	[status] [nvarchar](50) NOT NULL,
	[momo_order_id] [nvarchar](100) NULL,
	[momo_request_id] [nvarchar](100) NULL,
	[momo_trans_id] [nvarchar](100) NULL,
	[momo_pay_url] [nvarchar](500) NULL,
	[momo_response_data] [nvarchar](max) NULL,
	[cash_received] [decimal](10, 2) NULL,
	[cash_change] [decimal](10, 2) NULL,
	[payment_time] [datetime2](7) NULL,
	[created_at] [datetime2](7) NULL,
	[updated_at] [datetime2](7) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
/****** Object:  Table [dbo].[roles]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[roles](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](50) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[table_keys]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[table_keys](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[table_id] [int] NOT NULL,
	[key_value] [nvarchar](100) NOT NULL,
	[created_at] [datetime] NULL,
	[expires_at] [datetime] NULL,
	[is_valid] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[tables]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[tables](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[name] [nvarchar](50) NOT NULL,
	[status] [nvarchar](50) NULL,
	[is_buffet] [bit] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[users]    Script Date: 28/05/2025 05:17:41 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[users](
	[id] [int] IDENTITY(1,1) NOT NULL,
	[username] [nvarchar](50) NOT NULL,
	[password] [nvarchar](100) NOT NULL,
	[role_id] [int] NULL,
	[full_name] [nvarchar](100) NULL,
	[phone_number] [nvarchar](20) NULL,
	[age] [int] NULL,
	[email] [nvarchar](100) NULL,
	[address] [nvarchar](255) NULL,
	[created_at] [datetime] NULL,
	[updated_at] [datetime] NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
SET IDENTITY_INSERT [dbo].[categories] ON 

INSERT [dbo].[categories] ([id], [name], [is_drink]) VALUES (1, N'Món chính', 0)
INSERT [dbo].[categories] ([id], [name], [is_drink]) VALUES (2, N'Đồ uống', 1)
INSERT [dbo].[categories] ([id], [name], [is_drink]) VALUES (3, N'Tráng miệng', 0)
INSERT [dbo].[categories] ([id], [name], [is_drink]) VALUES (4, N'Mi nơ', 0)
SET IDENTITY_INSERT [dbo].[categories] OFF
GO
SET IDENTITY_INSERT [dbo].[food_ingredients] ON 

INSERT [dbo].[food_ingredients] ([id], [food_id], [ingredient_id], [amount]) VALUES (1, 1, 1, CAST(0.20 AS Decimal(10, 2)))
INSERT [dbo].[food_ingredients] ([id], [food_id], [ingredient_id], [amount]) VALUES (2, 1, 2, CAST(0.30 AS Decimal(10, 2)))
INSERT [dbo].[food_ingredients] ([id], [food_id], [ingredient_id], [amount]) VALUES (3, 2, 3, CAST(0.25 AS Decimal(10, 2)))
INSERT [dbo].[food_ingredients] ([id], [food_id], [ingredient_id], [amount]) VALUES (4, 2, 4, CAST(0.40 AS Decimal(10, 2)))
SET IDENTITY_INSERT [dbo].[food_ingredients] OFF
GO
SET IDENTITY_INSERT [dbo].[foods] ON 

INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (1, N'Phở bò', CAST(50000.00 AS Decimal(10, 2)), N'/api/images/foods/ea298d7e-64d7-4870-a413-f80ddd3e9378.jpg', 1)
INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (2, N'Cơm gà', CAST(40000.00 AS Decimal(10, 2)), N'/api/images/foods/f77de625-15bc-4498-bfe8-77c0f01933db.jpeg', 1)
INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (11, N'TT', CAST(1000.00 AS Decimal(10, 2)), N'/api/images/foods/becc56e6-62fe-4455-b63a-b75951edf879.jpg', 4)
INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (14, N'dd', CAST(1000.00 AS Decimal(10, 2)), N'/api/images/foods/7ed8a535-d215-4c55-9b79-14a007874c04.jpg', 4)
INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (15, N'Cà phê', CAST(29000.00 AS Decimal(10, 2)), N'/api/images/foods/4463147b-8dbb-4112-9162-3b3e5183ab00.jpg', 2)
INSERT [dbo].[foods] ([id], [name], [price], [image_url], [category_id]) VALUES (34, N'Trà tắc', CAST(39000.00 AS Decimal(10, 2)), N'/api/images/foods/6bab1a49-d2a9-491b-8dea-b05ae72e6f40.jpg', 2)
SET IDENTITY_INSERT [dbo].[foods] OFF
GO
SET IDENTITY_INSERT [dbo].[ingredients] ON 

INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (1, N'Thịt bò', N'kg', CAST(9.50 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (2, N'Bánh phở', N'kg', CAST(99.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (3, N'Gà', N'kg', CAST(8.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (4, N'Cơm trắng', N'kg', CAST(6.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (5, N'Trà túi lọc', N'gói', CAST(30.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (6, N'Đào hộp', N'kg', CAST(4.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (7, N'Kem', N'tấn', CAST(3.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (8, N'Vani', N'lít', CAST(2000.00 AS Decimal(10, 2)))
INSERT [dbo].[ingredients] ([id], [name], [unit], [quantity]) VALUES (10, N'Gạo', N'hạt', CAST(1.00 AS Decimal(10, 2)))
SET IDENTITY_INSERT [dbo].[ingredients] OFF
GO
SET IDENTITY_INSERT [dbo].[kitchen_queue] ON 

INSERT [dbo].[kitchen_queue] ([id], [order_detail_id], [status], [updated_at]) VALUES (18, 48, N'Ch? ch? bi?n', CAST(N'2025-05-28T05:08:21.493' AS DateTime))
INSERT [dbo].[kitchen_queue] ([id], [order_detail_id], [status], [updated_at]) VALUES (19, 49, N'Ch? ch? bi?n', CAST(N'2025-05-28T05:15:02.647' AS DateTime))
SET IDENTITY_INSERT [dbo].[kitchen_queue] OFF
GO
SET IDENTITY_INSERT [dbo].[logs] ON 

INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (212, 0, N'Khách bàn 1 đặt đơn hàng mới ID: 73', CAST(N'2025-04-13T01:27:24.317' AS DateTime), 1)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (213, 0, N'Khách bàn 1 hủy đơn hàng ID: 73', CAST(N'2025-04-13T01:27:53.810' AS DateTime), 1)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (234, 0, N'Khách bàn 1 đặt đơn hàng mới ID: 1', CAST(N'2025-04-13T23:31:46.050' AS DateTime), 1)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (239, 0, N'Khách bàn 1 đặt đơn hàng mới ID: 2', CAST(N'2025-04-14T22:22:57.990' AS DateTime), 1)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (243, 0, N'Khách bàn 2 đặt đơn hàng mới ID: 3', CAST(N'2025-04-15T09:54:32.093' AS DateTime), 2)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (272, 11, N'Đăng nhập', CAST(N'2025-05-21T17:36:49.533' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (273, 11, N'Đăng nhập', CAST(N'2025-05-21T18:31:04.460' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (274, 12, N'Đăng nhập', CAST(N'2025-05-21T18:48:39.957' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (275, 11, N'Đăng nhập', CAST(N'2025-05-21T18:55:59.157' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (276, 12, N'Đăng nhập', CAST(N'2025-05-21T18:59:57.373' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (277, 11, N'Đăng nhập', CAST(N'2025-05-21T19:01:40.523' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (278, 11, N'Đăng nhập', CAST(N'2025-05-21T19:06:47.880' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (279, 11, N'Đăng nhập', CAST(N'2025-05-21T19:47:21.670' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (280, 11, N'Đăng nhập', CAST(N'2025-05-21T20:07:24.390' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (281, 11, N'Đăng nhập', CAST(N'2025-05-21T20:21:56.117' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (282, 11, N'Đăng nhập', CAST(N'2025-05-21T20:32:29.317' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (283, 11, N'Đăng nhập', CAST(N'2025-05-21T20:50:38.667' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (284, 11, N'Đăng nhập', CAST(N'2025-05-21T21:21:41.123' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (285, 11, N'Đăng nhập', CAST(N'2025-05-21T23:20:03.620' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (286, 11, N'Đăng nhập', CAST(N'2025-05-21T23:35:03.520' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (287, 11, N'Đăng nhập', CAST(N'2025-05-21T23:37:27.957' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (288, 11, N'Đăng nhập', CAST(N'2025-05-24T17:01:09.873' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (289, 11, N'Đăng nhập', CAST(N'2025-05-24T17:06:07.573' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (290, 11, N'Đăng nhập', CAST(N'2025-05-24T17:30:51.677' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (291, 11, N'Đăng nhập', CAST(N'2025-05-24T23:34:53.573' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (292, 11, N'Đăng nhập', CAST(N'2025-05-24T23:53:05.493' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (293, 11, N'Đăng nhập', CAST(N'2025-05-25T00:47:39.420' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (294, 11, N'Đăng nhập', CAST(N'2025-05-25T00:48:03.053' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (295, 11, N'Đăng nhập', CAST(N'2025-05-25T01:15:20.147' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (296, 11, N'Đăng nhập', CAST(N'2025-05-25T01:20:26.623' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (297, 11, N'Đăng nhập', CAST(N'2025-05-25T01:20:26.623' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (298, 11, N'Đăng nhập', CAST(N'2025-05-25T01:25:01.770' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (299, 11, N'Đăng nhập', CAST(N'2025-05-25T01:29:04.693' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (300, 11, N'Đăng nhập', CAST(N'2025-05-25T02:05:57.987' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (301, 11, N'Đăng nhập', CAST(N'2025-05-25T02:19:26.960' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (302, 11, N'Đăng nhập', CAST(N'2025-05-25T02:19:48.700' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (303, 11, N'Đăng nhập', CAST(N'2025-05-25T02:30:49.527' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (304, 11, N'Đăng nhập', CAST(N'2025-05-25T02:31:40.020' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (305, 11, N'Đăng nhập', CAST(N'2025-05-25T02:34:29.127' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (306, 11, N'Đăng nhập', CAST(N'2025-05-25T02:48:47.570' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (307, 11, N'Đăng nhập', CAST(N'2025-05-26T15:45:46.293' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (308, 11, N'Đăng nhập', CAST(N'2025-05-26T17:12:41.767' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (309, 11, N'Đăng nhập', CAST(N'2025-05-26T17:17:53.927' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (310, 11, N'Xóa 3 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-26T17:18:04.293' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (311, 11, N'Đăng nhập', CAST(N'2025-05-26T18:50:41.623' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (312, 11, N'Xóa 1 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-26T21:05:42.047' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (313, 11, N'Xóa 1 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-26T21:17:34.767' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (314, 11, N'Đăng nhập', CAST(N'2025-05-26T21:39:33.393' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (315, 11, N'Đăng nhập', CAST(N'2025-05-26T21:47:11.350' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (316, 11, N'Đăng nhập', CAST(N'2025-05-26T22:12:18.993' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (317, 11, N'Đăng nhập', CAST(N'2025-05-26T22:12:41.610' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (318, 11, N'Đăng nhập', CAST(N'2025-05-27T01:57:00.450' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (319, 11, N'Đăng nhập', CAST(N'2025-05-28T01:25:14.503' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (320, 11, N'Xóa 1 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-28T01:48:06.943' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (321, 11, N'Đăng nhập', CAST(N'2025-05-28T04:02:58.840' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (322, 11, N'Đăng nhập', CAST(N'2025-05-28T04:13:12.150' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (323, 11, N'Xóa 6 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-28T04:23:50.370' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (324, 11, N'Đăng nhập', CAST(N'2025-05-28T04:28:53.887' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (325, 11, N'Xóa 1 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-28T04:29:05.427' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (326, 11, N'Đăng nhập', CAST(N'2025-05-28T04:59:49.467' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (327, 11, N'Xóa 3 món ăn đã hoàn thành khỏi hàng đợi nhà bếp', CAST(N'2025-05-28T05:06:08.007' AS DateTime), NULL)
INSERT [dbo].[logs] ([id], [user_id], [action], [created_at], [table_id]) VALUES (328, 11, N'Đăng nhập', CAST(N'2025-05-28T05:14:24.080' AS DateTime), NULL)
SET IDENTITY_INSERT [dbo].[logs] OFF
GO
SET IDENTITY_INSERT [dbo].[order_details] ON 

INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (1, 1, 1, 1, CAST(50000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (2, 2, 1, 5, CAST(50000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (3, 3, 1, 5, CAST(50000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (4, 3, 2, 3, CAST(40000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (5, 4, 14, 1, CAST(1000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (6, 7, 11, 1, CAST(1000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (7, 8, 14, 1, CAST(1000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (8, 9, 1, 1, CAST(50000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (12, 13, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (13, 14, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (14, 15, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (15, 16, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (17, 17, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (18, 17, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (19, 18, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (20, 18, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (21, 19, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (22, 19, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (23, 20, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (24, 20, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (25, 21, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (26, 21, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (27, 22, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (28, 22, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (29, 23, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (30, 23, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (31, 24, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (32, 24, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (33, 25, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (34, 25, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (35, 26, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (36, 26, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (37, 27, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (38, 27, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (39, 28, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (40, 28, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (41, 29, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (42, 29, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (43, 30, NULL, 1, CAST(299000.00 AS Decimal(10, 2)), N'Buffet - Ăn thoải mái tất cả món ăn', 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (44, 30, 34, 1, CAST(0.00 AS Decimal(10, 2)), NULL, 1)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (45, 31, 34, 1, CAST(39000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (46, 32, 34, 1, CAST(39000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (47, 33, 2, 1, CAST(40000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (48, 34, 15, 1, CAST(29000.00 AS Decimal(10, 2)), NULL, 0)
INSERT [dbo].[order_details] ([id], [order_id], [food_id], [quantity], [price], [custom_name], [is_free_drink]) VALUES (49, 35, 15, 1, CAST(29000.00 AS Decimal(10, 2)), NULL, 0)
SET IDENTITY_INSERT [dbo].[order_details] OFF
GO
SET IDENTITY_INSERT [dbo].[orders] ON 

INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (1, 1, NULL, CAST(N'2025-04-13T23:31:46.043' AS DateTime), N'Đã thanh toán', CAST(50000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (2, 1, NULL, CAST(N'2025-04-14T22:22:57.983' AS DateTime), N'Đã thanh toán', CAST(250000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (3, 2, NULL, CAST(N'2025-04-15T09:54:32.087' AS DateTime), N'Đã thanh toán', CAST(370000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (4, 1, NULL, CAST(N'2025-05-24T23:36:51.463' AS DateTime), N'Đã thanh toán', CAST(1000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (5, 1, 11, CAST(N'2025-05-25T01:25:25.943' AS DateTime), N'Đã thanh toán', CAST(500.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (6, 1, 11, CAST(N'2025-05-25T01:28:20.733' AS DateTime), N'Đã thanh toán', CAST(250.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (7, 1, NULL, CAST(N'2025-05-26T15:53:48.337' AS DateTime), N'Đã thanh toán', CAST(1000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (8, 2, NULL, CAST(N'2025-05-26T16:23:11.927' AS DateTime), N'Đã thanh toán', CAST(1000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (9, 1, NULL, CAST(N'2025-05-26T18:45:24.307' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, NULL, NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (11, 1, NULL, CAST(N'2025-05-26T21:20:40.260' AS DateTime), N'Đã thanh toán', CAST(1000.00 AS Decimal(18, 2)), 0, N'b63deb96-f17d-4f50-9aab-5ad793212b6a', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (13, 1, NULL, CAST(N'2025-05-26T21:34:17.783' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'6089bf51-d4f5-4d5c-a0f9-c8137e80c373', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (14, 1, NULL, CAST(N'2025-05-26T21:40:18.037' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'6835ae97-686c-4b36-9260-a5a1e99b14e9', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (15, 18, NULL, CAST(N'2025-05-26T22:28:12.397' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'37277467-105a-4359-a94c-23c9993e12f2', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (16, 1, NULL, CAST(N'2025-05-28T01:03:57.890' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'441b30e0-b7ff-4737-a239-d2141ab56a4b', N'buffet_1_1748369037816', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (17, 1, NULL, CAST(N'2025-05-28T01:08:34.160' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'63ac7fa0-258d-4580-af6b-93cf13dd235d', N'buffet_1_1748369314085', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (18, 1, NULL, CAST(N'2025-05-28T01:16:55.117' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'7f0ecffa-6547-4484-b1b5-5c2567fbaab8', N'buffet_1_1748369815077', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (19, 1, NULL, CAST(N'2025-05-28T01:17:05.683' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'7f0ecffa-6547-4484-b1b5-5c2567fbaab8', N'buffet_1_1748369825654', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (20, 2, NULL, CAST(N'2025-05-28T01:20:31.517' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'003f4984-2811-44a2-b3ea-76bd07a43134', N'buffet_2_1748370031477', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (21, 2, NULL, CAST(N'2025-05-28T01:20:34.400' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'003f4984-2811-44a2-b3ea-76bd07a43134', N'buffet_2_1748370034374', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (22, 2, NULL, CAST(N'2025-05-28T01:22:22.540' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'003f4984-2811-44a2-b3ea-76bd07a43134', N'buffet_2_1748370142442', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (23, 1, NULL, CAST(N'2025-05-28T01:47:46.507' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'8f4fffb1-82fb-447f-9e25-8cdcde774f43', N'buffet_1_1748371666480', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (24, 1, NULL, CAST(N'2025-05-28T03:55:29.990' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'89019ab2-6351-4be2-b4b8-9bc0250e2224', N'buffet_1_1748379329945', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (25, 2, NULL, CAST(N'2025-05-28T03:56:15.500' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'2146431c-649e-41b8-9ca1-239a7dc8215d', N'buffet_2_1748379375464', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (26, 1, NULL, CAST(N'2025-05-28T04:03:18.290' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'f39452f0-b00b-4f54-9326-916683874437', N'buffet_1_1748379798257', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (27, 2, NULL, CAST(N'2025-05-28T04:13:44.100' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'd1b719d5-fd08-4188-b524-7d897d7564b7', N'buffet_2_1748380424066', N'unpaid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (28, 2, NULL, CAST(N'2025-05-28T04:14:35.033' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 0, N'a902fb3d-94e0-4061-b4f5-9de9f9f152fd', N'buffet_2_1748380474995', N'unpaid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (29, 1, NULL, CAST(N'2025-05-28T04:20:58.583' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 1, N'7abaeba4-770b-4010-b6a3-0ac1bbbff5b1', N'buffet_1_1748380858539', N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (30, 3, NULL, CAST(N'2025-05-28T04:24:00.060' AS DateTime), N'Đã thanh toán', CAST(299000.00 AS Decimal(18, 2)), 1, N'76dc5dc3-4f17-4ca8-ba2b-432fc7ae5eb4', N'buffet_3_1748381040028', N'unpaid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (31, 1, NULL, CAST(N'2025-05-28T04:29:17.323' AS DateTime), N'Đã thanh toán', CAST(39000.00 AS Decimal(18, 2)), 0, N'4a761397-99d3-4316-acd4-dbf92e8d2d58', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (32, 1, NULL, CAST(N'2025-05-28T04:34:08.927' AS DateTime), N'Đã thanh toán', CAST(39000.00 AS Decimal(18, 2)), 0, N'62c13714-bf47-4820-bb36-8d2dbb0f7096', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (33, 1, NULL, CAST(N'2025-05-28T04:39:52.527' AS DateTime), N'Đã thanh toán', CAST(40000.00 AS Decimal(18, 2)), 0, N'd5f0c027-74e2-440c-9d45-4101516965ec', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (34, 1, NULL, CAST(N'2025-05-28T05:08:21.457' AS DateTime), N'Đã thanh toán', CAST(29000.00 AS Decimal(18, 2)), 0, N'06edf467-15b3-4b56-9bfc-754fcc99a4a0', NULL, N'paid')
INSERT [dbo].[orders] ([id], [table_id], [user_id], [order_time], [status], [total], [is_buffet], [table_key], [buffet_session_id], [payment_status]) VALUES (35, 1, NULL, CAST(N'2025-05-28T05:15:02.610' AS DateTime), N'Đang phục vụ', CAST(29000.00 AS Decimal(18, 2)), 0, N'f4b24832-3277-42ae-87fa-fdce29282fda', NULL, N'unpaid')
SET IDENTITY_INSERT [dbo].[orders] OFF
GO
SET IDENTITY_INSERT [dbo].[roles] ON 

INSERT [dbo].[roles] ([id], [name]) VALUES (1, N'admin')
INSERT [dbo].[roles] ([id], [name]) VALUES (3, N'cashier')
INSERT [dbo].[roles] ([id], [name]) VALUES (5, N'guest')
INSERT [dbo].[roles] ([id], [name]) VALUES (4, N'kitchen')
INSERT [dbo].[roles] ([id], [name]) VALUES (2, N'manager')
SET IDENTITY_INSERT [dbo].[roles] OFF
GO
SET IDENTITY_INSERT [dbo].[table_keys] ON 

INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (34, 3, N'de7b48d1-8999-47bc-b40e-28991e2a5a61', CAST(N'2025-05-18T15:11:44.220' AS DateTime), CAST(N'2025-05-18T16:11:44.217' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (35, 5, N'6fd76306-7439-4c1a-80a4-2cb5961ac3b6', CAST(N'2025-05-18T15:12:37.213' AS DateTime), CAST(N'2025-05-18T16:12:37.213' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (36, 7, N'b52656b5-8a77-419e-bc8c-bb5826f0d9b4', CAST(N'2025-05-18T15:23:05.153' AS DateTime), CAST(N'2025-05-18T16:23:05.153' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (37, 7, N'ae23a920-9b28-498b-a0a4-544d60c02e83', CAST(N'2025-05-18T15:23:27.403' AS DateTime), CAST(N'2025-05-18T16:23:27.403' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (38, 4, N'508fd7d2-f20d-4736-8220-8157711526a7', CAST(N'2025-05-18T16:40:53.157' AS DateTime), CAST(N'2025-05-18T17:40:53.157' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (39, 3, N'968acefc-3471-45cb-b3a2-94be1e650b57', CAST(N'2025-05-18T16:41:07.273' AS DateTime), CAST(N'2025-05-18T17:41:07.273' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (40, 4, N'acb48768-10fc-4a7f-a87f-6362224de604', CAST(N'2025-05-21T14:43:37.000' AS DateTime), CAST(N'2025-05-21T15:43:37.000' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (41, 4, N'd5dc2a54-d80c-451c-92a0-11e2fe341fb4', CAST(N'2025-05-21T14:43:40.930' AS DateTime), CAST(N'2025-05-21T15:43:40.930' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (42, 6, N'739e25ee-ecee-4863-8bfc-cf70e00f2e65', CAST(N'2025-05-21T15:32:53.977' AS DateTime), CAST(N'2025-05-21T16:32:53.977' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (43, 6, N'c97903de-6ea6-476c-8c12-23f6fade75b5', CAST(N'2025-05-21T15:33:00.093' AS DateTime), CAST(N'2025-05-21T16:33:00.093' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (44, 9, N'147cc105-7181-42a8-bb1e-fc71cb8346a1', CAST(N'2025-05-21T15:34:33.657' AS DateTime), CAST(N'2025-05-21T16:34:33.657' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (45, 6, N'23978577-9917-40a2-af63-3b2981114cd7', CAST(N'2025-05-21T15:50:33.083' AS DateTime), CAST(N'2025-05-21T16:50:33.083' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (46, 8, N'c6929dd8-916a-4bf1-b845-30480fbbcd50', CAST(N'2025-05-21T15:51:03.897' AS DateTime), CAST(N'2025-05-21T16:51:03.897' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (47, 8, N'a0b2e2d6-cee3-40ab-9dca-47f4f4873305', CAST(N'2025-05-21T15:59:09.090' AS DateTime), CAST(N'2025-05-21T16:59:09.090' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (67, 2, N'c9e69bbf-aef9-4f63-b815-092386f8c54d', CAST(N'2025-05-26T16:22:25.667' AS DateTime), CAST(N'2025-05-26T18:22:25.667' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (75, 2, N'fe2fac36-8570-4bd2-b77c-2a4dc0a11ef6', CAST(N'2025-05-26T21:25:05.247' AS DateTime), CAST(N'2025-05-26T23:25:05.247' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (77, 1, N'6089bf51-d4f5-4d5c-a0f9-c8137e80c373', CAST(N'2025-05-26T21:34:15.147' AS DateTime), CAST(N'2025-05-26T23:34:15.147' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (78, 1, N'6835ae97-686c-4b36-9260-a5a1e99b14e9', CAST(N'2025-05-26T21:40:14.937' AS DateTime), CAST(N'2025-05-26T23:40:14.937' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (79, 18, N'37277467-105a-4359-a94c-23c9993e12f2', CAST(N'2025-05-26T22:28:06.953' AS DateTime), CAST(N'2025-05-27T00:28:06.953' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (80, 1, N'73fbf1f5-19ac-46c9-bc5b-7459528231c2', CAST(N'2025-05-26T22:37:48.857' AS DateTime), CAST(N'2025-05-27T00:37:48.857' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (81, 1, N'2a526c93-f035-4a5a-9814-8b27380c2e9c', CAST(N'2025-05-26T22:37:51.893' AS DateTime), CAST(N'2025-05-27T00:37:51.893' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (82, 1, N'c4cacbbc-2232-407a-8ac9-520568e61e8c', CAST(N'2025-05-26T22:42:32.383' AS DateTime), CAST(N'2025-05-27T00:42:32.383' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (83, 1, N'3e876b9f-fcfd-4b00-a477-917f283992cb', CAST(N'2025-05-26T22:43:03.567' AS DateTime), CAST(N'2025-05-27T00:43:03.567' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (84, 1, N'c0218587-585e-4482-8988-933f619e3d4f', CAST(N'2025-05-26T22:45:05.147' AS DateTime), CAST(N'2025-05-27T00:45:05.147' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (85, 1, N'cbbcd297-9eb0-494f-95ae-8220995add26', CAST(N'2025-05-26T22:45:09.847' AS DateTime), CAST(N'2025-05-27T00:45:09.847' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (86, 1, N'216e965e-0af6-4bcf-9f77-0e758fa2b6d7', CAST(N'2025-05-27T01:57:36.887' AS DateTime), CAST(N'2025-05-27T03:57:36.887' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (87, 1, N'707fda28-2f66-4011-bfff-6b22bf25f3ae', CAST(N'2025-05-27T01:59:57.540' AS DateTime), CAST(N'2025-05-27T03:59:57.540' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (88, 1, N'dcb466d0-b3bb-4a20-999c-db6e252f069f', CAST(N'2025-05-27T02:03:07.403' AS DateTime), CAST(N'2025-05-27T04:03:07.403' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (89, 1, N'37b46fcb-c840-4a2e-996c-9f46053a390f', CAST(N'2025-05-27T13:01:44.083' AS DateTime), CAST(N'2025-05-27T15:01:44.083' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (90, 1, N'c1eee778-2b02-453b-b53b-b0d22810544c', CAST(N'2025-05-27T13:02:17.310' AS DateTime), CAST(N'2025-05-27T15:02:17.310' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (91, 1, N'441b30e0-b7ff-4737-a239-d2141ab56a4b', CAST(N'2025-05-28T00:58:12.787' AS DateTime), CAST(N'2025-05-28T02:58:12.787' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (92, 1, N'63ac7fa0-258d-4580-af6b-93cf13dd235d', CAST(N'2025-05-28T01:08:21.417' AS DateTime), CAST(N'2025-05-28T03:08:21.417' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (93, 1, N'7f0ecffa-6547-4484-b1b5-5c2567fbaab8', CAST(N'2025-05-28T01:16:51.473' AS DateTime), CAST(N'2025-05-28T03:16:51.473' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (94, 2, N'003f4984-2811-44a2-b3ea-76bd07a43134', CAST(N'2025-05-28T01:20:28.060' AS DateTime), CAST(N'2025-05-28T03:20:28.060' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (95, 4, N'2ff0b520-a109-4e00-8bb2-25cb1d53290d', CAST(N'2025-05-28T01:27:31.697' AS DateTime), CAST(N'2025-05-28T03:27:31.697' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (96, 1, N'2bf496b1-9989-4b4b-943e-5abc32730cd7', CAST(N'2025-05-28T01:42:30.737' AS DateTime), CAST(N'2025-05-28T03:42:30.737' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (97, 1, N'8f4fffb1-82fb-447f-9e25-8cdcde774f43', CAST(N'2025-05-28T01:47:39.470' AS DateTime), CAST(N'2025-05-28T03:47:39.470' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (98, 1, N'89019ab2-6351-4be2-b4b8-9bc0250e2224', CAST(N'2025-05-28T03:55:25.677' AS DateTime), CAST(N'2025-05-28T05:55:25.677' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (99, 2, N'2146431c-649e-41b8-9ca1-239a7dc8215d', CAST(N'2025-05-28T03:56:11.527' AS DateTime), CAST(N'2025-05-28T05:56:11.527' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (100, 1, N'f39452f0-b00b-4f54-9326-916683874437', CAST(N'2025-05-28T04:03:15.327' AS DateTime), CAST(N'2025-05-28T06:03:15.323' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (101, 2, N'd1b719d5-fd08-4188-b524-7d897d7564b7', CAST(N'2025-05-28T04:13:41.163' AS DateTime), CAST(N'2025-05-28T06:13:41.163' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (102, 2, N'a902fb3d-94e0-4061-b4f5-9de9f9f152fd', CAST(N'2025-05-28T04:14:23.897' AS DateTime), CAST(N'2025-05-28T06:14:23.897' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (103, 1, N'7abaeba4-770b-4010-b6a3-0ac1bbbff5b1', CAST(N'2025-05-28T04:20:55.583' AS DateTime), CAST(N'2025-05-28T06:20:55.583' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (104, 3, N'76dc5dc3-4f17-4ca8-ba2b-432fc7ae5eb4', CAST(N'2025-05-28T04:23:56.897' AS DateTime), CAST(N'2025-05-28T06:23:56.897' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (105, 1, N'4a761397-99d3-4316-acd4-dbf92e8d2d58', CAST(N'2025-05-28T04:29:12.847' AS DateTime), CAST(N'2025-05-28T06:29:12.847' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (106, 1, N'62c13714-bf47-4820-bb36-8d2dbb0f7096', CAST(N'2025-05-28T04:34:04.363' AS DateTime), CAST(N'2025-05-28T06:34:04.363' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (107, 1, N'd5f0c027-74e2-440c-9d45-4101516965ec', CAST(N'2025-05-28T04:39:41.930' AS DateTime), CAST(N'2025-05-28T06:39:41.930' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (108, 1, N'06edf467-15b3-4b56-9bfc-754fcc99a4a0', CAST(N'2025-05-28T05:06:12.020' AS DateTime), CAST(N'2025-05-28T07:06:12.020' AS DateTime), 0)
INSERT [dbo].[table_keys] ([id], [table_id], [key_value], [created_at], [expires_at], [is_valid]) VALUES (109, 1, N'f4b24832-3277-42ae-87fa-fdce29282fda', CAST(N'2025-05-28T05:14:57.413' AS DateTime), CAST(N'2025-05-28T07:14:57.413' AS DateTime), 1)
SET IDENTITY_INSERT [dbo].[table_keys] OFF
GO
SET IDENTITY_INSERT [dbo].[tables] ON 

INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (1, N'Bàn 1', N'Đang phục vụ', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (2, N'Bàn 2', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (3, N'Bàn 3', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (4, N'Bàn 4', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (5, N'Bàn 5', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (6, N'Bàn 6', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (7, N'Bàn 7', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (8, N'Bàn 8', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (9, N'Bàn 9', N'Trống', 0)
INSERT [dbo].[tables] ([id], [name], [status], [is_buffet]) VALUES (18, N'Bàn 10', N'Trống', 0)
SET IDENTITY_INSERT [dbo].[tables] OFF
GO
SET IDENTITY_INSERT [dbo].[users] ON 

INSERT [dbo].[users] ([id], [username], [password], [role_id], [full_name], [phone_number], [age], [email], [address], [created_at], [updated_at]) VALUES (0, N'guest', N'guest', 5, N'Khách', N'0123456789', 25, N'<EMAIL>', N'123 Đường Khách, Quận 1, TP.HCM', CAST(N'2025-04-15T07:03:41.113' AS DateTime), CAST(N'2025-04-15T07:03:41.113' AS DateTime))
INSERT [dbo].[users] ([id], [username], [password], [role_id], [full_name], [phone_number], [age], [email], [address], [created_at], [updated_at]) VALUES (11, N'admin11', N'$2b$10$4bWWnogCHE2gHKDnzu7fCOp3BpUK4gDJMBJQRX69OutnOwPmSkGtG', 1, N'Dương Huy', N'0792967973', 16, N'<EMAIL>', N'Copac Square', CAST(N'2025-05-21T17:35:56.643' AS DateTime), CAST(N'2025-05-21T17:35:56.643' AS DateTime))
INSERT [dbo].[users] ([id], [username], [password], [role_id], [full_name], [phone_number], [age], [email], [address], [created_at], [updated_at]) VALUES (12, N'kitchen', N'$2b$10$7RdqzJvmQ9lyXVygTwFEFuuHYhcneyoeAwOhb/PRoetf5K9LUuS..', 3, N'Dương Văn Huy', N'0792967973', 18, N'<EMAIL>', N'abc', CAST(N'2025-05-21T17:37:38.813' AS DateTime), CAST(N'2025-05-21T19:30:05.840' AS DateTime))
SET IDENTITY_INSERT [dbo].[users] OFF
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ__roles__72E12F1B210C0DC2]    Script Date: 28/05/2025 05:17:41 ******/
ALTER TABLE [dbo].[roles] ADD UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ__roles__72E12F1BC716578C]    Script Date: 28/05/2025 05:17:41 ******/
ALTER TABLE [dbo].[roles] ADD UNIQUE NONCLUSTERED 
(
	[name] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ_table_keys_key_value]    Script Date: 28/05/2025 05:17:41 ******/
ALTER TABLE [dbo].[table_keys] ADD  CONSTRAINT [UQ_table_keys_key_value] UNIQUE NONCLUSTERED 
(
	[key_value] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ__users__F3DBC5721406BC28]    Script Date: 28/05/2025 05:17:41 ******/
ALTER TABLE [dbo].[users] ADD UNIQUE NONCLUSTERED 
(
	[username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
SET ANSI_PADDING ON
GO
/****** Object:  Index [UQ__users__F3DBC57263462CC2]    Script Date: 28/05/2025 05:17:41 ******/
ALTER TABLE [dbo].[users] ADD UNIQUE NONCLUSTERED 
(
	[username] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
GO
ALTER TABLE [dbo].[categories] ADD  DEFAULT ((0)) FOR [is_drink]
GO
ALTER TABLE [dbo].[kitchen_queue] ADD  DEFAULT ('Ch? ch? bi?n') FOR [status]
GO
ALTER TABLE [dbo].[kitchen_queue] ADD  DEFAULT (getdate()) FOR [updated_at]
GO
ALTER TABLE [dbo].[logs] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[order_details] ADD  DEFAULT ((0)) FOR [is_free_drink]
GO
ALTER TABLE [dbo].[orders] ADD  DEFAULT (getdate()) FOR [order_time]
GO
ALTER TABLE [dbo].[orders] ADD  DEFAULT ('Ðang ph?c v?') FOR [status]
GO
ALTER TABLE [dbo].[orders] ADD  DEFAULT ((0)) FOR [is_buffet]
GO
ALTER TABLE [dbo].[orders] ADD  DEFAULT ('unpaid') FOR [payment_status]
GO
ALTER TABLE [dbo].[payments] ADD  DEFAULT ('pending') FOR [status]
GO
ALTER TABLE [dbo].[payments] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[payments] ADD  DEFAULT (getdate()) FOR [updated_at]
GO
ALTER TABLE [dbo].[table_keys] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[table_keys] ADD  DEFAULT ((1)) FOR [is_valid]
GO
ALTER TABLE [dbo].[tables] ADD  DEFAULT ('Tr?ng') FOR [status]
GO
ALTER TABLE [dbo].[tables] ADD  DEFAULT ((0)) FOR [is_buffet]
GO
ALTER TABLE [dbo].[users] ADD  DEFAULT (getdate()) FOR [created_at]
GO
ALTER TABLE [dbo].[food_ingredients]  WITH CHECK ADD FOREIGN KEY([food_id])
REFERENCES [dbo].[foods] ([id])
GO
ALTER TABLE [dbo].[food_ingredients]  WITH CHECK ADD FOREIGN KEY([food_id])
REFERENCES [dbo].[foods] ([id])
GO
ALTER TABLE [dbo].[food_ingredients]  WITH CHECK ADD FOREIGN KEY([ingredient_id])
REFERENCES [dbo].[ingredients] ([id])
GO
ALTER TABLE [dbo].[food_ingredients]  WITH CHECK ADD FOREIGN KEY([ingredient_id])
REFERENCES [dbo].[ingredients] ([id])
GO
ALTER TABLE [dbo].[foods]  WITH CHECK ADD FOREIGN KEY([category_id])
REFERENCES [dbo].[categories] ([id])
GO
ALTER TABLE [dbo].[foods]  WITH CHECK ADD FOREIGN KEY([category_id])
REFERENCES [dbo].[categories] ([id])
GO
ALTER TABLE [dbo].[kitchen_queue]  WITH CHECK ADD FOREIGN KEY([order_detail_id])
REFERENCES [dbo].[order_details] ([id])
GO
ALTER TABLE [dbo].[kitchen_queue]  WITH CHECK ADD FOREIGN KEY([order_detail_id])
REFERENCES [dbo].[order_details] ([id])
GO
ALTER TABLE [dbo].[logs]  WITH CHECK ADD FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
GO
ALTER TABLE [dbo].[logs]  WITH CHECK ADD FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
GO
ALTER TABLE [dbo].[order_details]  WITH CHECK ADD FOREIGN KEY([food_id])
REFERENCES [dbo].[foods] ([id])
GO
ALTER TABLE [dbo].[order_details]  WITH CHECK ADD FOREIGN KEY([food_id])
REFERENCES [dbo].[foods] ([id])
GO
ALTER TABLE [dbo].[order_details]  WITH CHECK ADD FOREIGN KEY([order_id])
REFERENCES [dbo].[orders] ([id])
GO
ALTER TABLE [dbo].[order_details]  WITH CHECK ADD FOREIGN KEY([order_id])
REFERENCES [dbo].[orders] ([id])
GO
ALTER TABLE [dbo].[orders]  WITH CHECK ADD FOREIGN KEY([table_id])
REFERENCES [dbo].[tables] ([id])
GO
ALTER TABLE [dbo].[orders]  WITH CHECK ADD FOREIGN KEY([table_id])
REFERENCES [dbo].[tables] ([id])
GO
ALTER TABLE [dbo].[orders]  WITH CHECK ADD FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
GO
ALTER TABLE [dbo].[orders]  WITH CHECK ADD FOREIGN KEY([user_id])
REFERENCES [dbo].[users] ([id])
GO
ALTER TABLE [dbo].[payments]  WITH CHECK ADD FOREIGN KEY([order_id])
REFERENCES [dbo].[orders] ([id])
GO
ALTER TABLE [dbo].[table_keys]  WITH CHECK ADD FOREIGN KEY([table_id])
REFERENCES [dbo].[tables] ([id])
GO
ALTER TABLE [dbo].[table_keys]  WITH CHECK ADD FOREIGN KEY([table_id])
REFERENCES [dbo].[tables] ([id])
GO
ALTER TABLE [dbo].[users]  WITH CHECK ADD  CONSTRAINT [FK_users_roles] FOREIGN KEY([role_id])
REFERENCES [dbo].[roles] ([id])
GO
ALTER TABLE [dbo].[users] CHECK CONSTRAINT [FK_users_roles]
GO
ALTER TABLE [dbo].[users]  WITH CHECK ADD  CONSTRAINT [CK_users_age] CHECK  (([age]>=(16) AND [age]<=(100)))
GO
ALTER TABLE [dbo].[users] CHECK CONSTRAINT [CK_users_age]
GO
ALTER TABLE [dbo].[users]  WITH CHECK ADD  CONSTRAINT [CK_users_email_format] CHECK  (([email] like '%_@_%._%'))
GO
ALTER TABLE [dbo].[users] CHECK CONSTRAINT [CK_users_email_format]
GO
