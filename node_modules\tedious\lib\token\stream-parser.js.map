{"version": 3, "file": "stream-parser.js", "names": ["_token", "require", "_colmetadataTokenParser", "_interopRequireDefault", "_doneT<PERSON><PERSON><PERSON>er", "_envChangeTokenParser", "_infoerrorTokenParser", "_fedauthInfoParser", "_featureExtAckParser", "_loginackToken<PERSON><PERSON>er", "_orderT<PERSON><PERSON><PERSON>er", "_returnstatusTokenParser", "_returnvalueToken<PERSON><PERSON>er", "_rowT<PERSON><PERSON><PERSON><PERSON>", "_nb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_sspiToken<PERSON><PERSON>er", "_helpers", "obj", "__esModule", "default", "<PERSON><PERSON><PERSON>", "debug", "colMetadata", "options", "iterator", "buffer", "position", "parseTokens", "iterable", "parser", "waitForChunk", "err", "length", "type", "readUInt8", "token", "readToken", "undefined", "TYPE", "DONE", "readDoneToken", "DONEPROC", "readDoneProcToken", "DONEINPROC", "readDoneInProcToken", "ERROR", "readErrorToken", "INFO", "readInfoToken", "ENVCHANGE", "readEnvChangeToken", "LOGINACK", "readLoginAckToken", "RETURNSTATUS", "readReturnStatusToken", "ORDER", "readOrderToken", "FEDAUTHINFO", "readFedAuthInfoToken", "SSPI", "readSSPIToken", "COLMETADATA", "readColMetadataToken", "RETURNVALUE", "readReturnValueToken", "ROW", "readRowToken", "NBCROW", "readNbcRowToken", "FEATUREEXTACK", "readFeatureExtAckToken", "Error", "result", "featureExtAckParser", "NotEnoughDataError", "then", "offset", "value", "nbc<PERSON>ow<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "colMetadataParser", "columns", "s<PERSON>i<PERSON><PERSON><PERSON>", "fedAuthInfoParser", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "login<PERSON><PERSON><PERSON><PERSON><PERSON>", "env<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "infoParser", "error<PERSON><PERSON>er", "doneInProcParser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "Symbol", "asyncIterator", "call", "<PERSON><PERSON><PERSON>", "alloc", "next", "done", "concat", "slice", "_default", "exports", "module"], "sources": ["../../src/token/stream-parser.ts"], "sourcesContent": ["import Debug from '../debug';\nimport { type InternalConnectionOptions } from '../connection';\n\nimport { TYPE, ColMetadataToken, DoneProcToken, DoneToken, DoneInProcToken, ErrorMessageToken, InfoMessageToken, RowToken, type EnvChangeToken, LoginAckToken, ReturnStatusToken, OrderToken, FedAuthInfoToken, SSPIToken, ReturnValueToken, NBCRowToken, FeatureExtAckToken, Token } from './token';\n\nimport colMetadataParser, { type ColumnMetadata } from './colmetadata-token-parser';\nimport { doneParser, doneInProcParser, doneProcParser } from './done-token-parser';\nimport envChangeParser from './env-change-token-parser';\nimport { errorParser, infoParser } from './infoerror-token-parser';\nimport fedAuthInfoParser from './fedauth-info-parser';\nimport featureExtAckParser from './feature-ext-ack-parser';\nimport loginAckParser from './loginack-token-parser';\nimport orderParser from './order-token-parser';\nimport returnStatusParser from './returnstatus-token-parser';\nimport returnValueParser from './returnvalue-token-parser';\nimport rowParser from './row-token-parser';\nimport nbcRowParser from './nbcrow-token-parser';\nimport sspiParser from './sspi-token-parser';\nimport { NotEnoughDataError } from './helpers';\n\nexport type ParserOptions = Pick<InternalConnectionOptions, 'useUTC' | 'lowerCaseGuids' | 'tdsVersion' | 'useColumnNames' | 'columnNameReplacer' | 'camelCaseColumns'>;\n\nclass Parser {\n  debug: Debug;\n  colMetadata: ColumnMetadata[];\n  options: ParserOptions;\n\n  iterator: AsyncIterator<Buffer, any, undefined> | Iterator<Buffer, any, undefined>;\n  buffer: Buffer;\n  position: number;\n\n  static async *parseTokens(iterable: AsyncIterable<Buffer> | Iterable<Buffer>, debug: Debug, options: ParserOptions, colMetadata: ColumnMetadata[] = []) {\n    const parser = new Parser(iterable, debug, options);\n    parser.colMetadata = colMetadata;\n\n    while (true) {\n      try {\n        await parser.waitForChunk();\n      } catch (err: unknown) {\n        if (parser.position === parser.buffer.length) {\n          return;\n        }\n\n        throw err;\n      }\n\n      while (parser.buffer.length >= parser.position + 1) {\n        const type = parser.buffer.readUInt8(parser.position);\n        parser.position += 1;\n\n        const token = parser.readToken(type);\n        if (token !== undefined) {\n          yield token;\n        }\n      }\n    }\n  }\n\n  readToken(type: number): Token | undefined | Promise<Token | undefined> {\n    switch (type) {\n      case TYPE.DONE: {\n        return this.readDoneToken();\n      }\n\n      case TYPE.DONEPROC: {\n        return this.readDoneProcToken();\n      }\n\n      case TYPE.DONEINPROC: {\n        return this.readDoneInProcToken();\n      }\n\n      case TYPE.ERROR: {\n        return this.readErrorToken();\n      }\n\n      case TYPE.INFO: {\n        return this.readInfoToken();\n      }\n\n      case TYPE.ENVCHANGE: {\n        return this.readEnvChangeToken();\n      }\n\n      case TYPE.LOGINACK: {\n        return this.readLoginAckToken();\n      }\n\n      case TYPE.RETURNSTATUS: {\n        return this.readReturnStatusToken();\n      }\n\n      case TYPE.ORDER: {\n        return this.readOrderToken();\n      }\n\n      case TYPE.FEDAUTHINFO: {\n        return this.readFedAuthInfoToken();\n      }\n\n      case TYPE.SSPI: {\n        return this.readSSPIToken();\n      }\n\n      case TYPE.COLMETADATA: {\n        return this.readColMetadataToken();\n      }\n\n      case TYPE.RETURNVALUE: {\n        return this.readReturnValueToken();\n      }\n\n      case TYPE.ROW: {\n        return this.readRowToken();\n      }\n\n      case TYPE.NBCROW: {\n        return this.readNbcRowToken();\n      }\n\n      case TYPE.FEATUREEXTACK: {\n        return this.readFeatureExtAckToken();\n      }\n\n      default: {\n        throw new Error('Unknown type: ' + type);\n      }\n    }\n  }\n\n  readFeatureExtAckToken(): FeatureExtAckToken | Promise<FeatureExtAckToken> {\n    let result;\n\n    try {\n      result = featureExtAckParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readFeatureExtAckToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  async readNbcRowToken(): Promise<NBCRowToken> {\n    return await nbcRowParser(this);\n  }\n\n  async readReturnValueToken(): Promise<ReturnValueToken> {\n    return await returnValueParser(this);\n  }\n\n  async readColMetadataToken(): Promise<ColMetadataToken> {\n    const token = await colMetadataParser(this);\n    this.colMetadata = token.columns;\n    return token;\n  }\n\n  readSSPIToken(): SSPIToken | Promise<SSPIToken> {\n    let result;\n\n    try {\n      result = sspiParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readSSPIToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readFedAuthInfoToken(): FedAuthInfoToken | Promise<FedAuthInfoToken> {\n    let result;\n\n    try {\n      result = fedAuthInfoParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readFedAuthInfoToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readOrderToken(): OrderToken | Promise<OrderToken> {\n    let result;\n\n    try {\n      result = orderParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readOrderToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readReturnStatusToken(): ReturnStatusToken | Promise<ReturnStatusToken> {\n    let result;\n\n    try {\n      result = returnStatusParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readReturnStatusToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readLoginAckToken(): LoginAckToken | Promise<LoginAckToken> {\n    let result;\n\n    try {\n      result = loginAckParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readLoginAckToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readEnvChangeToken(): EnvChangeToken | undefined | Promise<EnvChangeToken | undefined> {\n    let result;\n\n    try {\n      result = envChangeParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readEnvChangeToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readRowToken(): RowToken | Promise<RowToken> {\n    return rowParser(this);\n  }\n\n  readInfoToken(): InfoMessageToken | Promise<InfoMessageToken> {\n    let result;\n\n    try {\n      result = infoParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readInfoToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readErrorToken(): ErrorMessageToken | Promise<ErrorMessageToken> {\n    let result;\n\n    try {\n      result = errorParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readErrorToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readDoneInProcToken(): DoneInProcToken | Promise<DoneInProcToken> {\n    let result;\n\n    try {\n      result = doneInProcParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readDoneInProcToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readDoneProcToken(): DoneProcToken | Promise<DoneProcToken> {\n    let result;\n\n    try {\n      result = doneProcParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readDoneProcToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  readDoneToken(): DoneToken | Promise<DoneToken> {\n    let result;\n\n    try {\n      result = doneParser(this.buffer, this.position, this.options);\n    } catch (err: any) {\n      if (err instanceof NotEnoughDataError) {\n        return this.waitForChunk().then(() => {\n          return this.readDoneToken();\n        });\n      }\n\n      throw err;\n    }\n\n    this.position = result.offset;\n    return result.value;\n  }\n\n  constructor(iterable: AsyncIterable<Buffer> | Iterable<Buffer>, debug: Debug, options: ParserOptions) {\n    this.debug = debug;\n    this.colMetadata = [];\n    this.options = options;\n\n    this.iterator = ((iterable as AsyncIterable<Buffer>)[Symbol.asyncIterator] || (iterable as Iterable<Buffer>)[Symbol.iterator]).call(iterable);\n\n    this.buffer = Buffer.alloc(0);\n    this.position = 0;\n  }\n\n  async waitForChunk() {\n    const result = await this.iterator.next();\n    if (result.done) {\n      throw new Error('unexpected end of data');\n    }\n\n    if (this.position === this.buffer.length) {\n      this.buffer = result.value;\n    } else {\n      this.buffer = Buffer.concat([this.buffer.slice(this.position), result.value]);\n    }\n\n    this.position = 0;\n  }\n}\n\nexport default Parser;\nmodule.exports = Parser;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,uBAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AACA,IAAAI,qBAAA,GAAAF,sBAAA,CAAAF,OAAA;AACA,IAAAK,qBAAA,GAAAL,OAAA;AACA,IAAAM,kBAAA,GAAAJ,sBAAA,CAAAF,OAAA;AACA,IAAAO,oBAAA,GAAAL,sBAAA,CAAAF,OAAA;AACA,IAAAQ,oBAAA,GAAAN,sBAAA,CAAAF,OAAA;AACA,IAAAS,iBAAA,GAAAP,sBAAA,CAAAF,OAAA;AACA,IAAAU,wBAAA,GAAAR,sBAAA,CAAAF,OAAA;AACA,IAAAW,uBAAA,GAAAT,sBAAA,CAAAF,OAAA;AACA,IAAAY,eAAA,GAAAV,sBAAA,CAAAF,OAAA;AACA,IAAAa,kBAAA,GAAAX,sBAAA,CAAAF,OAAA;AACA,IAAAc,gBAAA,GAAAZ,sBAAA,CAAAF,OAAA;AACA,IAAAe,QAAA,GAAAf,OAAA;AAA+C,SAAAE,uBAAAc,GAAA,WAAAA,GAAA,IAAAA,GAAA,CAAAC,UAAA,GAAAD,GAAA,KAAAE,OAAA,EAAAF,GAAA;AAI/C,MAAMG,MAAM,CAAC;EACXC,KAAK;EACLC,WAAW;EACXC,OAAO;EAEPC,QAAQ;EACRC,MAAM;EACNC,QAAQ;EAER,cAAcC,WAAWA,CAACC,QAAkD,EAAEP,KAAY,EAAEE,OAAsB,EAAED,WAA6B,GAAG,EAAE,EAAE;IACtJ,MAAMO,MAAM,GAAG,IAAIT,MAAM,CAACQ,QAAQ,EAAEP,KAAK,EAAEE,OAAO,CAAC;IACnDM,MAAM,CAACP,WAAW,GAAGA,WAAW;IAEhC,OAAO,IAAI,EAAE;MACX,IAAI;QACF,MAAMO,MAAM,CAACC,YAAY,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOC,GAAY,EAAE;QACrB,IAAIF,MAAM,CAACH,QAAQ,KAAKG,MAAM,CAACJ,MAAM,CAACO,MAAM,EAAE;UAC5C;QACF;QAEA,MAAMD,GAAG;MACX;MAEA,OAAOF,MAAM,CAACJ,MAAM,CAACO,MAAM,IAAIH,MAAM,CAACH,QAAQ,GAAG,CAAC,EAAE;QAClD,MAAMO,IAAI,GAAGJ,MAAM,CAACJ,MAAM,CAACS,SAAS,CAACL,MAAM,CAACH,QAAQ,CAAC;QACrDG,MAAM,CAACH,QAAQ,IAAI,CAAC;QAEpB,MAAMS,KAAK,GAAGN,MAAM,CAACO,SAAS,CAACH,IAAI,CAAC;QACpC,IAAIE,KAAK,KAAKE,SAAS,EAAE;UACvB,MAAMF,KAAK;QACb;MACF;IACF;EACF;EAEAC,SAASA,CAACH,IAAY,EAAkD;IACtE,QAAQA,IAAI;MACV,KAAKK,WAAI,CAACC,IAAI;QAAE;UACd,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC;QAC7B;MAEA,KAAKF,WAAI,CAACG,QAAQ;QAAE;UAClB,OAAO,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACjC;MAEA,KAAKJ,WAAI,CAACK,UAAU;QAAE;UACpB,OAAO,IAAI,CAACC,mBAAmB,CAAC,CAAC;QACnC;MAEA,KAAKN,WAAI,CAACO,KAAK;QAAE;UACf,OAAO,IAAI,CAACC,cAAc,CAAC,CAAC;QAC9B;MAEA,KAAKR,WAAI,CAACS,IAAI;QAAE;UACd,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC;QAC7B;MAEA,KAAKV,WAAI,CAACW,SAAS;QAAE;UACnB,OAAO,IAAI,CAACC,kBAAkB,CAAC,CAAC;QAClC;MAEA,KAAKZ,WAAI,CAACa,QAAQ;QAAE;UAClB,OAAO,IAAI,CAACC,iBAAiB,CAAC,CAAC;QACjC;MAEA,KAAKd,WAAI,CAACe,YAAY;QAAE;UACtB,OAAO,IAAI,CAACC,qBAAqB,CAAC,CAAC;QACrC;MAEA,KAAKhB,WAAI,CAACiB,KAAK;QAAE;UACf,OAAO,IAAI,CAACC,cAAc,CAAC,CAAC;QAC9B;MAEA,KAAKlB,WAAI,CAACmB,WAAW;QAAE;UACrB,OAAO,IAAI,CAACC,oBAAoB,CAAC,CAAC;QACpC;MAEA,KAAKpB,WAAI,CAACqB,IAAI;QAAE;UACd,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC;QAC7B;MAEA,KAAKtB,WAAI,CAACuB,WAAW;QAAE;UACrB,OAAO,IAAI,CAACC,oBAAoB,CAAC,CAAC;QACpC;MAEA,KAAKxB,WAAI,CAACyB,WAAW;QAAE;UACrB,OAAO,IAAI,CAACC,oBAAoB,CAAC,CAAC;QACpC;MAEA,KAAK1B,WAAI,CAAC2B,GAAG;QAAE;UACb,OAAO,IAAI,CAACC,YAAY,CAAC,CAAC;QAC5B;MAEA,KAAK5B,WAAI,CAAC6B,MAAM;QAAE;UAChB,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;QAC/B;MAEA,KAAK9B,WAAI,CAAC+B,aAAa;QAAE;UACvB,OAAO,IAAI,CAACC,sBAAsB,CAAC,CAAC;QACtC;MAEA;QAAS;UACP,MAAM,IAAIC,KAAK,CAAC,gBAAgB,GAAGtC,IAAI,CAAC;QAC1C;IACF;EACF;EAEAqC,sBAAsBA,CAAA,EAAqD;IACzE,IAAIE,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAC,4BAAmB,EAAC,IAAI,CAAChD,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACxE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACL,sBAAsB,CAAC,CAAC;QACtC,CAAC,CAAC;MACJ;MAEA,MAAMvC,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEA,MAAMT,eAAeA,CAAA,EAAyB;IAC5C,OAAO,MAAM,IAAAU,0BAAY,EAAC,IAAI,CAAC;EACjC;EAEA,MAAMd,oBAAoBA,CAAA,EAA8B;IACtD,OAAO,MAAM,IAAAe,+BAAiB,EAAC,IAAI,CAAC;EACtC;EAEA,MAAMjB,oBAAoBA,CAAA,EAA8B;IACtD,MAAM3B,KAAK,GAAG,MAAM,IAAA6C,+BAAiB,EAAC,IAAI,CAAC;IAC3C,IAAI,CAAC1D,WAAW,GAAGa,KAAK,CAAC8C,OAAO;IAChC,OAAO9C,KAAK;EACd;EAEAyB,aAAaA,CAAA,EAAmC;IAC9C,IAAIY,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAU,wBAAU,EAAC,IAAI,CAACzD,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAC/D,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACf,aAAa,CAAC,CAAC;QAC7B,CAAC,CAAC;MACJ;MAEA,MAAM7B,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAnB,oBAAoBA,CAAA,EAAiD;IACnE,IAAIc,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAW,0BAAiB,EAAC,IAAI,CAAC1D,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACtE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACjB,oBAAoB,CAAC,CAAC;QACpC,CAAC,CAAC;MACJ;MAEA,MAAM3B,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEArB,cAAcA,CAAA,EAAqC;IACjD,IAAIgB,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAY,yBAAW,EAAC,IAAI,CAAC3D,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAChE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACnB,cAAc,CAAC,CAAC;QAC9B,CAAC,CAAC;MACJ;MAEA,MAAMzB,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAvB,qBAAqBA,CAAA,EAAmD;IACtE,IAAIkB,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAa,gCAAkB,EAAC,IAAI,CAAC5D,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACvE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACrB,qBAAqB,CAAC,CAAC;QACrC,CAAC,CAAC;MACJ;MAEA,MAAMvB,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAzB,iBAAiBA,CAAA,EAA2C;IAC1D,IAAIoB,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAc,4BAAc,EAAC,IAAI,CAAC7D,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACnE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACvB,iBAAiB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ;MAEA,MAAMrB,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEA3B,kBAAkBA,CAAA,EAAqE;IACrF,IAAIsB,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAe,6BAAe,EAAC,IAAI,CAAC9D,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACpE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACzB,kBAAkB,CAAC,CAAC;QAClC,CAAC,CAAC;MACJ;MAEA,MAAMnB,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAX,YAAYA,CAAA,EAAiC;IAC3C,OAAO,IAAAsB,uBAAS,EAAC,IAAI,CAAC;EACxB;EAEAxC,aAAaA,CAAA,EAAiD;IAC5D,IAAIwB,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAiB,gCAAU,EAAC,IAAI,CAAChE,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAC/D,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAAC3B,aAAa,CAAC,CAAC;QAC7B,CAAC,CAAC;MACJ;MAEA,MAAMjB,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEA/B,cAAcA,CAAA,EAAmD;IAC/D,IAAI0B,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAkB,iCAAW,EAAC,IAAI,CAACjE,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAChE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAAC7B,cAAc,CAAC,CAAC;QAC9B,CAAC,CAAC;MACJ;MAEA,MAAMf,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAjC,mBAAmBA,CAAA,EAA+C;IAChE,IAAI4B,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAmB,iCAAgB,EAAC,IAAI,CAAClE,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACrE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAAC/B,mBAAmB,CAAC,CAAC;QACnC,CAAC,CAAC;MACJ;MAEA,MAAMb,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAnC,iBAAiBA,CAAA,EAA2C;IAC1D,IAAI8B,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAoB,+BAAc,EAAC,IAAI,CAACnE,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IACnE,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACjC,iBAAiB,CAAC,CAAC;QACjC,CAAC,CAAC;MACJ;MAEA,MAAMX,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEArC,aAAaA,CAAA,EAAmC;IAC9C,IAAIgC,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG,IAAAqB,2BAAU,EAAC,IAAI,CAACpE,MAAM,EAAE,IAAI,CAACC,QAAQ,EAAE,IAAI,CAACH,OAAO,CAAC;IAC/D,CAAC,CAAC,OAAOQ,GAAQ,EAAE;MACjB,IAAIA,GAAG,YAAY2C,2BAAkB,EAAE;QACrC,OAAO,IAAI,CAAC5C,YAAY,CAAC,CAAC,CAAC6C,IAAI,CAAC,MAAM;UACpC,OAAO,IAAI,CAACnC,aAAa,CAAC,CAAC;QAC7B,CAAC,CAAC;MACJ;MAEA,MAAMT,GAAG;IACX;IAEA,IAAI,CAACL,QAAQ,GAAG8C,MAAM,CAACI,MAAM;IAC7B,OAAOJ,MAAM,CAACK,KAAK;EACrB;EAEAiB,WAAWA,CAAClE,QAAkD,EAAEP,KAAY,EAAEE,OAAsB,EAAE;IACpG,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,WAAW,GAAG,EAAE;IACrB,IAAI,CAACC,OAAO,GAAGA,OAAO;IAEtB,IAAI,CAACC,QAAQ,GAAG,CAAEI,QAAQ,CAA2BmE,MAAM,CAACC,aAAa,CAAC,IAAKpE,QAAQ,CAAsBmE,MAAM,CAACvE,QAAQ,CAAC,EAAEyE,IAAI,CAACrE,QAAQ,CAAC;IAE7I,IAAI,CAACH,MAAM,GAAGyE,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACzE,QAAQ,GAAG,CAAC;EACnB;EAEA,MAAMI,YAAYA,CAAA,EAAG;IACnB,MAAM0C,MAAM,GAAG,MAAM,IAAI,CAAChD,QAAQ,CAAC4E,IAAI,CAAC,CAAC;IACzC,IAAI5B,MAAM,CAAC6B,IAAI,EAAE;MACf,MAAM,IAAI9B,KAAK,CAAC,wBAAwB,CAAC;IAC3C;IAEA,IAAI,IAAI,CAAC7C,QAAQ,KAAK,IAAI,CAACD,MAAM,CAACO,MAAM,EAAE;MACxC,IAAI,CAACP,MAAM,GAAG+C,MAAM,CAACK,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACpD,MAAM,GAAGyE,MAAM,CAACI,MAAM,CAAC,CAAC,IAAI,CAAC7E,MAAM,CAAC8E,KAAK,CAAC,IAAI,CAAC7E,QAAQ,CAAC,EAAE8C,MAAM,CAACK,KAAK,CAAC,CAAC;IAC/E;IAEA,IAAI,CAACnD,QAAQ,GAAG,CAAC;EACnB;AACF;AAAC,IAAA8E,QAAA,GAAAC,OAAA,CAAAtF,OAAA,GAEcC,MAAM;AACrBsF,MAAM,CAACD,OAAO,GAAGrF,MAAM"}