require('dotenv').config({ path: '../.env' });
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const { v4: uuidv4 } = require('uuid');
const QRCode = require('qrcode');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const axios = require('axios');
const os = require('os');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.TABLE_SERVICE_PORT || 3011;

// Function để lấy địa chỉ IP của máy
function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      // Bỏ qua địa chỉ internal (127.x.x.x) và IPv6
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost'; // fallback
}

// Import shared modules
const { db, auth } = require('../shared');

// Middleware
app.use(cors());
app.use(helmet({
  contentSecurityPolicy: false, // Tắt CSP để cho phép inline scripts
  crossOriginEmbedderPolicy: false
}));
app.use(morgan('dev'));
app.use(express.json());

// Serve static files cho web đặt món
app.use('/public', express.static(path.join(__dirname, 'public')));

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  socket.on('join_table', (tableId) => {
    socket.join(`table_${tableId}`);
    console.log(`Client ${socket.id} joined table ${tableId}`);
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

// Serve static files
app.use('/public', express.static(path.join(__dirname, 'public')));

// Routes

// Middleware kiểm tra key cho web đặt món
async function validateOrderKey(req, res, next) {
  try {
    const { table_id, key } = req.query;

    if (!table_id) {
      return res.status(400).send(`
        <html>
          <head><title>Lỗi</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h2>❌ Thiếu thông tin bàn</h2>
            <p>Vui lòng quét lại mã QR để truy cập.</p>
          </body>
        </html>
      `);
    }

    if (!key) {
      return res.status(400).send(`
        <html>
          <head><title>Lỗi</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h2>❌ Thiếu mã truy cập</h2>
            <p>Vui lòng quét lại mã QR để truy cập.</p>
          </body>
        </html>
      `);
    }

    // Kiểm tra key hợp lệ (tạm thời bỏ kiểm tra hết hạn để test)
    console.log('Kiểm tra key đặt món:', { table_id, key });
    const keyResult = await db.executeQuery(`
      SELECT tk.*, t.name as table_name
      FROM table_keys tk
      JOIN tables t ON tk.table_id = t.id
      WHERE tk.table_id = @tableId
        AND tk.key_value = @key
        AND tk.is_valid = @isValid
    `, [
      { name: 'tableId', type: db.sql.Int, value: table_id },
      { name: 'key', type: db.sql.NVarChar(100), value: key },
      { name: 'isValid', type: db.sql.Bit, value: true }
    ]);

    console.log('Kết quả kiểm tra key:', keyResult.recordset);

    if (keyResult.recordset.length === 0) {
      return res.status(403).send(`
        <html>
          <head><title>Mã truy cập không hợp lệ</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h2>🚫 Mã truy cập không hợp lệ hoặc đã hết hạn</h2>
            <p>Vui lòng quét lại mã QR để tiếp tục đặt món.</p>
            <p><small>Key: ${key}</small></p>
          </body>
        </html>
      `);
    }

    // Lưu thông tin key vào request
    req.tableKey = keyResult.recordset[0];
    next();
  } catch (error) {
    console.error('Error validating order key:', error);
    res.status(500).send(`
      <html>
        <head><title>Lỗi hệ thống</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
          <h2>❌ Lỗi hệ thống</h2>
          <p>Vui lòng thử lại sau.</p>
        </body>
      </html>
    `);
  }
}

// Route cho web đặt món khách hàng với kiểm tra key
app.get('/order', validateOrderKey, (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route cho trang kết quả thanh toán MoMo
app.get('/payment/momo/return', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'payment-return.html'));
});

// API để lấy thông tin bàn cho web đặt món
app.get('/api/customer/table/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.executeQuery('SELECT *, CASE WHEN is_buffet = 1 THEN \'Bàn Buffet\' ELSE \'Bàn Thường\' END as table_type FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching table info:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin bàn' });
  }
});

// API để lấy menu cho khách hàng
app.get('/api/customer/menu', async (req, res) => {
  try {
    // Lấy danh mục
    const categoriesResult = await db.executeQuery('SELECT * FROM categories ORDER BY name');

    // Lấy món ăn
    const foodsResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      ORDER BY c.name, f.name
    `);

    const categories = categoriesResult.recordset;
    const foods = foodsResult.recordset;

    // Nhóm món ăn theo danh mục
    const menu = categories.map(category => ({
      ...category,
      foods: foods.filter(food => food.category_id === category.id)
    }));

    res.json(menu);
  } catch (error) {
    console.error('Error fetching menu:', error);
    res.status(500).json({ message: 'Lỗi khi lấy menu' });
  }
});

// API để lấy giá buffet cho khách hàng
app.get('/api/customer/buffet-price', async (req, res) => {
  try {
    // Gọi Order Service để lấy giá buffet
    const response = await axios.get(`http://localhost:${process.env.ORDER_SERVICE_PORT}/api/buffet-price`);
    res.json(response.data);
  } catch (error) {
    console.error('Error fetching buffet price:', error);
    res.status(500).json({ message: 'Lỗi khi lấy giá buffet' });
  }
});

// API mới: Lấy menu phân biệt món ăn và đồ uống cho buffet
app.get('/api/customer/buffet-menu', async (req, res) => {
  try {
    console.log('🍹 API buffet-menu được gọi');

    // Lấy danh mục
    const categoriesResult = await db.executeQuery('SELECT * FROM categories ORDER BY is_drink, name');

    // Lấy món ăn và đồ uống
    const foodsResult = await db.executeQuery(`
      SELECT f.*, c.name as category_name, c.is_drink
      FROM foods f
      LEFT JOIN categories c ON f.category_id = c.id
      ORDER BY c.is_drink, c.name, f.name
    `);

    const categories = categoriesResult.recordset;
    const foods = foodsResult.recordset;

    console.log('📊 Categories:', categories.length);
    console.log('🍽️ Foods:', foods.length);

    // Phân loại món ăn và đồ uống
    const foodCategories = categories.filter(cat => !cat.is_drink).map(category => ({
      ...category,
      foods: foods.filter(food => food.category_id === category.id)
    }));

    const drinkCategories = categories.filter(cat => cat.is_drink).map(category => ({
      ...category,
      foods: foods.filter(food => food.category_id === category.id)
    }));

    console.log('🍽️ Food categories:', foodCategories.length);
    console.log('🍹 Drink categories:', drinkCategories.length);
    console.log('🍹 Drinks detail:', drinkCategories);

    res.json({
      foods: foodCategories,
      drinks: drinkCategories
    });
  } catch (error) {
    console.error('Error fetching buffet menu:', error);
    res.status(500).json({ message: 'Lỗi khi lấy menu buffet' });
  }
});

// API để tạo đơn hàng từ web khách hàng
app.post('/api/customer/orders', async (req, res) => {
  try {
    const { table_id, items, table_key, is_buffet, buffet_price } = req.body;

    if (!table_id || !items || items.length === 0) {
      return res.status(400).json({ message: 'Thiếu thông tin bàn hoặc món ăn' });
    }

    // Gọi Order Service để tạo đơn hàng với table_key
    const orderResponse = await axios.post(`http://localhost:${process.env.ORDER_SERVICE_PORT}/api/orders`, {
      table_id,
      items,
      table_key, // Truyền key để xác thực
      user_id: null, // Khách hàng không cần đăng nhập
      is_buffet, // Thêm thông tin buffet
      buffet_price // Thêm giá buffet
    });

    // Thông báo real-time cho bàn
    io.to(`table_${table_id}`).emit('order_created', {
      order: orderResponse.data,
      message: 'Đơn hàng của bạn đã được gửi đến nhà bếp!'
    });

    res.json(orderResponse.data);
  } catch (error) {
    console.error('Error creating customer order:', error);
    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({ message: 'Lỗi khi tạo đơn hàng' });
    }
  }
});

// API mới: Tạo đơn hàng buffet với món nước miễn phí
app.post('/api/customer/buffet-orders', async (req, res) => {
  try {
    const { table_id, table_key, free_drink_id, buffet_price } = req.body;

    if (!table_id || !free_drink_id) {
      return res.status(400).json({ message: 'Thiếu thông tin bàn hoặc món nước miễn phí' });
    }

    // Tạo buffet session ID duy nhất
    const buffetSessionId = `buffet_${table_id}_${Date.now()}`;

    // Gọi Order Service để tạo đơn hàng buffet với món nước miễn phí
    const orderResponse = await axios.post(`http://localhost:${process.env.ORDER_SERVICE_PORT}/api/buffet-orders`, {
      table_id,
      table_key,
      free_drink_id,
      buffet_price,
      buffet_session_id: buffetSessionId,
      user_id: null
    });

    // Thông báo real-time cho bàn
    io.to(`table_${table_id}`).emit('buffet_order_created', {
      order: orderResponse.data,
      message: 'Đặt buffet thành công! Bạn có thể gọi thêm món ăn.'
    });

    res.json(orderResponse.data);
  } catch (error) {
    console.error('Error creating buffet order:', error);
    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({ message: 'Lỗi khi tạo đơn hàng buffet' });
    }
  }
});

// API mới: Thêm món ăn cho buffet (chỉ món ăn, không phải đồ uống)
app.post('/api/customer/buffet-add-food', async (req, res) => {
  try {
    const { table_id, table_key, items } = req.body;

    if (!table_id || !items || items.length === 0) {
      return res.status(400).json({ message: 'Thiếu thông tin bàn hoặc món ăn' });
    }

    // Gọi Order Service để thêm món ăn cho buffet
    const orderResponse = await axios.post(`http://localhost:${process.env.ORDER_SERVICE_PORT}/api/buffet-add-food`, {
      table_id,
      table_key,
      items
    });

    // Thông báo real-time cho bàn
    io.to(`table_${table_id}`).emit('buffet_food_added', {
      order: orderResponse.data,
      message: 'Đã thêm món ăn vào buffet!'
    });

    res.json(orderResponse.data);
  } catch (error) {
    console.error('Error adding food to buffet:', error);
    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({ message: 'Lỗi khi thêm món ăn cho buffet' });
    }
  }
});

// API để lấy đơn hàng hiện tại của bàn theo key
app.get('/api/customer/orders/:table_id', async (req, res) => {
  try {
    const { table_id } = req.params;
    const { key } = req.query; // Lấy key từ query parameter

    console.log(`🔍 Lấy đơn hàng cho bàn ${table_id} với key: ${key}`);

    let orderQuery = `
      SELECT o.*, t.name as table_name,
             COALESCE(o.is_buffet, 0) as is_buffet,
             COALESCE(o.payment_status, 'unpaid') as payment_status
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      WHERE o.table_id = @tableId
        AND o.status NOT IN ('Đã thanh toán', 'Hoàn thành', 'Đã hoàn thành')
    `;

    const queryParams = [{ name: 'tableId', type: db.sql.Int, value: table_id }];

    // Nếu không có key, trả về null luôn - không cho phép truy cập không có key
    if (!key) {
      console.log(`❌ Không có key - không trả về đơn hàng nào`);
      return res.json(null);
    }

    // CHỈ lấy đơn hàng của key hiện tại
    orderQuery += ` AND o.table_key = @tableKey`;
    queryParams.push({ name: 'tableKey', type: db.sql.NVarChar(100), value: key });
    console.log(`🔑 CHỈ lấy đơn hàng của key: ${key}`);
    orderQuery += ` ORDER BY o.order_time DESC`;

    const orderResult = await db.executeQuery(orderQuery, queryParams);

    if (orderResult.recordset.length === 0) {
      console.log(`❌ Không tìm thấy đơn hàng cho bàn ${table_id}${key ? ` với key ${key}` : ''}`);
      return res.json(null);
    }

    const order = orderResult.recordset[0];
    console.log(`✅ Tìm thấy đơn hàng #${order.id} cho bàn ${table_id} - is_buffet: ${order.is_buffet}, key: ${order.table_key}`);

    // Lấy chi tiết đơn hàng (bao gồm cả buffet)
    const detailsResult = await db.executeQuery(`
      SELECT od.*,
             COALESCE(od.custom_name, f.name) as food_name,
             f.image_url as food_image
      FROM order_details od
      LEFT JOIN foods f ON od.food_id = f.id
      WHERE od.order_id = @orderId
    `, [{ name: 'orderId', type: db.sql.Int, value: order.id }]);

    order.details = detailsResult.recordset;

    res.json(order);
  } catch (error) {
    console.error('Error fetching customer order:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin đơn hàng' });
  }
});

// Hàm kiểm tra và cập nhật trạng thái bàn dựa trên key hợp lệ (logic đơn giản)
async function updateTableStatusBasedOnKeys() {
  try {
    console.log('🔄 Kiểm tra trạng thái bàn dựa trên keys...');

    // Lấy tất cả keys hợp lệ trong 2 tiếng gần nhất
    const twoHoursAgo = new Date();
    twoHoursAgo.setTime(twoHoursAgo.getTime() - (2 * 60 * 60 * 1000));

    const validKeysResult = await db.executeQuery(`
      SELECT DISTINCT table_id FROM table_keys
      WHERE is_valid = 1
        AND created_at >= @twoHoursAgo
        AND expires_at > @now
    `, [
      { name: 'twoHoursAgo', type: db.sql.DateTime, value: twoHoursAgo },
      { name: 'now', type: db.sql.DateTime, value: new Date() }
    ]);

    const tablesWithValidKeys = validKeysResult.recordset.map(row => row.table_id);
    console.log(`🔑 Bàn có key hợp lệ:`, tablesWithValidKeys);

    // Lấy tất cả bàn
    const allTablesResult = await db.executeQuery(`SELECT id, name, status FROM tables`);

    for (const table of allTablesResult.recordset) {
      const hasValidKey = tablesWithValidKeys.includes(table.id);
      const shouldBeServing = hasValidKey;
      const currentStatus = table.status;

      if (shouldBeServing && currentStatus !== 'Đang phục vụ') {
        // Cập nhật về "Đang phục vụ"
        await db.executeQuery(`
          UPDATE tables SET status = N'Đang phục vụ' WHERE id = @tableId
        `, [
          { name: 'tableId', type: db.sql.Int, value: table.id }
        ]);
        console.log(`✅ Cập nhật bàn ${table.name}: Trống → Đang phục vụ`);

      } else if (!shouldBeServing && currentStatus === 'Đang phục vụ') {
        // Cập nhật về "Trống"
        await db.executeQuery(`
          UPDATE tables SET status = N'Trống' WHERE id = @tableId
        `, [
          { name: 'tableId', type: db.sql.Int, value: table.id }
        ]);
        console.log(`✅ Cập nhật bàn ${table.name}: Đang phục vụ → Trống`);
      }
    }
  } catch (error) {
    console.error('❌ Lỗi khi cập nhật trạng thái bàn:', error);
  }
}

// Thiết lập timer kiểm tra định kỳ trạng thái bàn
setInterval(updateTableStatusBasedOnKeys, 10000); // Kiểm tra mỗi 10 giây

// Lấy danh sách tất cả các bàn
app.get('/api/tables', auth.authenticateToken, async (req, res) => {
  try {
    console.log('🔍 API /api/tables được gọi - bắt đầu cập nhật trạng thái bàn');

    // Cập nhật trạng thái bàn trước khi trả về
    await updateTableStatusBasedOnKeys();

    const result = await db.executeQuery('SELECT * FROM tables');
    console.log('📋 Danh sách bàn trả về:', result.recordset);

    res.json(result.recordset);
  } catch (error) {
    console.error('Error fetching tables:', error);
    res.status(500).json({ message: 'Lỗi khi lấy danh sách bàn' });
  }
});

// Lấy thông tin một bàn cụ thể
app.get('/api/tables/:id', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const result = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error fetching table:', error);
    res.status(500).json({ message: 'Lỗi khi lấy thông tin bàn' });
  }
});

// Tạo bàn mới
app.post('/api/tables', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { name, status = 'Trống', is_buffet = false } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Tên bàn là bắt buộc' });
    }

    const result = await db.executeQuery(
      'INSERT INTO tables (name, status, is_buffet) OUTPUT INSERTED.* VALUES (@name, @status, @isBuffet)',
      [
        { name: 'name', type: db.sql.NVarChar(50), value: name },
        { name: 'status', type: db.sql.NVarChar(50), value: status },
        { name: 'isBuffet', type: db.sql.Bit, value: is_buffet }
      ]
    );

    res.status(201).json(result.recordset[0]);
  } catch (error) {
    console.error('Error creating table:', error);
    res.status(500).json({ message: 'Lỗi khi tạo bàn mới' });
  }
});

// Cập nhật thông tin bàn
app.put('/api/tables/:id', auth.authenticateToken, auth.authorizeRole([1, 2]), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, status, is_buffet } = req.body;

    if (!name && !status && is_buffet === undefined) {
      return res.status(400).json({ message: 'Cần cung cấp ít nhất một trường để cập nhật' });
    }

    let query = 'UPDATE tables SET ';
    const params = [];

    if (name) {
      query += 'name = @name, ';
      params.push({ name: 'name', type: db.sql.NVarChar(50), value: name });
    }

    if (status) {
      query += 'status = @status, ';
      params.push({ name: 'status', type: db.sql.NVarChar(50), value: status });
    }

    if (is_buffet !== undefined) {
      query += 'is_buffet = @isBuffet, ';
      params.push({ name: 'isBuffet', type: db.sql.Bit, value: is_buffet });
    }

    // Xóa dấu phẩy cuối cùng
    query = query.slice(0, -2);

    query += ' OUTPUT INSERTED.* WHERE id = @id';
    params.push({ name: 'id', type: db.sql.Int, value: id });

    const result = await db.executeQuery(query, params);

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    res.json(result.recordset[0]);
  } catch (error) {
    console.error('Error updating table:', error);
    res.status(500).json({ message: 'Lỗi khi cập nhật thông tin bàn' });
  }
});

// Xóa bàn
app.delete('/api/tables/:id', auth.authenticateToken, auth.authorizeRole([1]), async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra xem bàn có đang được sử dụng không
    const checkResult = await db.executeQuery(
      'SELECT COUNT(*) as count FROM orders WHERE table_id = @id AND status != \'Đã thanh toán\'',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (checkResult.recordset[0].count > 0) {
      return res.status(400).json({ message: 'Không thể xóa bàn đang được sử dụng' });
    }

    const result = await db.executeQuery(
      'DELETE FROM tables OUTPUT DELETED.* WHERE id = @id',
      [{ name: 'id', type: db.sql.Int, value: id }]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    res.json({ message: 'Đã xóa bàn thành công', table: result.recordset[0] });
  } catch (error) {
    console.error('Error deleting table:', error);
    res.status(500).json({ message: 'Lỗi khi xóa bàn' });
  }
});

// Tạo QR code tĩnh cho bàn (dành cho in ấn)
app.post('/api/tables/:id/static-qrcode', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    const table = tableResult.recordset[0];

    // Tạo URL tĩnh cho endpoint generate key - khi quét sẽ tự động tạo key mới
    const localIP = getLocalIPAddress();
    const qrUrl = `http://${localIP}:${PORT}/api/tables/${id}/generate-access`;

    // Tạo QR code tĩnh
    const qrCodeImage = await QRCode.toDataURL(qrUrl, {
      width: 300,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });

    res.json({
      table_id: id,
      table_name: table.name,
      qr_code: qrCodeImage,
      url: qrUrl,
      type: 'static'
    });
  } catch (error) {
    console.error('Error generating static QR code:', error);
    res.status(500).json({ message: 'Lỗi khi tạo QR code tĩnh' });
  }
});

// Tạo QR code động cho bàn (có thời hạn) - tạm thời bỏ auth để test
app.post('/api/tables/:id/qrcode', async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    // Tạm thời bỏ kiểm tra status để test
    // if (tableResult.recordset[0].status !== 'Trống') {
    //   return res.status(400).json({ message: 'Bàn đang được sử dụng, không thể tạo QR code mới' });
    // }

    // Tạo key mới
    const keyValue = uuidv4();
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + (2 * 60 * 60 * 1000)); // 2 giờ

    // Lưu key vào database
    try {
      console.log('Đang lưu key:', {
        tableId: id,
        keyValue: keyValue,
        expiresAt: expiresAt
      });

      const result = await db.executeQuery(
        'INSERT INTO table_keys (table_id, key_value, created_at, expires_at, is_valid) OUTPUT INSERTED.* VALUES (@tableId, @keyValue, @createdAt, @expiresAt, @isValid)',
        [
          { name: 'tableId', type: db.sql.Int, value: id },
          { name: 'keyValue', type: db.sql.NVarChar(100), value: keyValue },
          { name: 'createdAt', type: db.sql.DateTime, value: new Date() },
          { name: 'expiresAt', type: db.sql.DateTime, value: expiresAt },
          { name: 'isValid', type: db.sql.Bit, value: true }
        ]
      );
      console.log('Key đã được lưu thành công:', result.recordset[0]);
    } catch (error) {
      console.error('Lỗi khi lưu key:', error);
      throw error;
    }

    // Tạo URL cho QR code sử dụng IP thực
    const localIP = getLocalIPAddress();
    const qrUrl = `http://${localIP}:${PORT}/api/tables/access/${keyValue}`;

    // Tạo QR code
    const qrCodeImage = await QRCode.toDataURL(qrUrl);

    res.json({
      table_id: id,
      key: keyValue,
      expires_at: expiresAt,
      qr_code: qrCodeImage,
      url: qrUrl,
      type: 'dynamic'
    });
  } catch (error) {
    console.error('Error generating QR code:', error);
    res.status(500).json({ message: 'Lỗi khi tạo QR code' });
  }
});

// API để vô hiệu hóa key và cập nhật trạng thái bàn
app.post('/api/tables/:id/invalidate-keys', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Vô hiệu hóa tất cả key của bàn
    await db.executeQuery(`
      UPDATE table_keys SET is_valid = 0
      WHERE table_id = @tableId AND is_valid = 1
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);

    // Reset trạng thái bàn về "Trống" và is_buffet = false
    await db.executeQuery(`
      UPDATE tables
      SET status = N'Trống', is_buffet = 0
      WHERE id = @tableId
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);

    // Lấy trạng thái bàn mới
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    console.log(`✅ Đã vô hiệu hóa key và reset bàn ${id} về trạng thái trống`);

    res.json({
      message: 'Đã vô hiệu hóa key và reset trạng thái bàn',
      table: tableResult.recordset[0]
    });
  } catch (error) {
    console.error('Error invalidating keys:', error);
    res.status(500).json({ message: 'Lỗi khi vô hiệu hóa key' });
  }
});

// API kiểm tra trạng thái key của bàn
app.get('/api/tables/:id/key-status', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    const table = tableResult.recordset[0];

    // Kiểm tra key hợp lệ hiện tại
    const validKeyResult = await db.executeQuery(`
      SELECT tk.*,
             CASE
               WHEN tk.expires_at > GETDATE() AND tk.is_valid = 1 THEN 1
               ELSE 0
             END as is_currently_valid
      FROM table_keys tk
      WHERE tk.table_id = @tableId
        AND tk.is_valid = 1
      ORDER BY tk.created_at DESC
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);

    const hasValidKey = validKeyResult.recordset.length > 0 &&
                       validKeyResult.recordset[0].is_currently_valid === 1;

    const keyInfo = validKeyResult.recordset.length > 0 ? validKeyResult.recordset[0] : null;

    res.json({
      table_id: parseInt(id),
      table_name: table.name,
      table_status: table.status,
      has_valid_key: hasValidKey,
      can_generate_qr: !hasValidKey, // Chỉ cho phép tạo QR khi không có key hợp lệ
      key_info: keyInfo ? {
        key_value: keyInfo.key_value,
        created_at: keyInfo.created_at,
        expires_at: keyInfo.expires_at,
        is_currently_valid: keyInfo.is_currently_valid === 1
      } : null
    });

  } catch (error) {
    console.error('Error checking table key status:', error);
    res.status(500).json({ message: 'Lỗi khi kiểm tra trạng thái key' });
  }
});

// API vô hiệu hóa tất cả key của bàn
app.post('/api/tables/:id/invalidate-keys', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    // Vô hiệu hóa tất cả key của bàn
    await db.executeQuery(`
      UPDATE table_keys
      SET is_valid = 0
      WHERE table_id = @tableId AND is_valid = 1
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);

    console.log(`🔒 Admin ${req.user.username} đã vô hiệu hóa tất cả key của bàn ${id}`);

    res.json({
      message: 'Đã vô hiệu hóa tất cả key của bàn',
      table_id: parseInt(id)
    });

  } catch (error) {
    console.error('Error invalidating table keys:', error);
    res.status(500).json({ message: 'Lỗi khi vô hiệu hóa key' });
  }
});

// Endpoint để generate key mới khi quét QR code tĩnh
app.get('/api/tables/:id/generate-access', async (req, res) => {
  try {
    const { id } = req.params;
    console.log('Generate access key cho bàn:', id);

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).send(`
        <html>
          <head><title>Bàn không tồn tại</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h2>❌ Bàn không tồn tại</h2>
            <p>Vui lòng kiểm tra lại QR code.</p>
          </body>
        </html>
      `);
    }

    const table = tableResult.recordset[0];

    // 🔑 KIỂM TRA KEY HỢP LỆ HIỆN TẠI TRƯỚC KHI TẠO MỚI
    const existingKeyResult = await db.executeQuery(`
      SELECT tk.*,
             CASE
               WHEN tk.expires_at > GETDATE() AND tk.is_valid = 1 THEN 1
               ELSE 0
             END as is_currently_valid
      FROM table_keys tk
      WHERE tk.table_id = @tableId
        AND tk.is_valid = 1
        AND tk.expires_at > GETDATE()
      ORDER BY tk.created_at DESC
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);

    // 🚫 NẾU BÀN ĐANG ĐƯỢC SỬ DỤNG, HIỂN THỊ THÔNG BÁO THAY VÌ TẠO KEY MỚI
    if (existingKeyResult.recordset.length > 0) {
      const existingKey = existingKeyResult.recordset[0];
      const expiresAt = new Date(existingKey.expires_at);
      const timeLeft = Math.max(0, Math.floor((expiresAt - new Date()) / (1000 * 60))); // phút

      console.log(`🚫 Bàn ${id} đang được sử dụng - từ chối tạo key mới`);

      // Hiển thị trang thông báo bàn đang được sử dụng
      return res.status(409).send(`
        <html>
          <head>
            <title>Bàn đang được sử dụng</title>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
              body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                margin: 0;
                padding: 20px;
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
              }
              .container {
                background: white;
                border-radius: 15px;
                padding: 40px;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                max-width: 400px;
                width: 100%;
              }
              .icon {
                font-size: 4em;
                margin-bottom: 20px;
                color: #ff6b6b;
              }
              h1 {
                color: #333;
                margin-bottom: 15px;
                font-size: 1.8em;
              }
              .message {
                color: #666;
                margin-bottom: 20px;
                line-height: 1.6;
              }
              .table-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #ff6b6b;
              }
              .time-left {
                color: #e74c3c;
                font-weight: bold;
                font-size: 1.1em;
              }
              .btn {
                background: #667eea;
                color: white;
                padding: 12px 25px;
                border: none;
                border-radius: 8px;
                font-size: 1em;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                margin-top: 15px;
                transition: background 0.3s;
              }
              .btn:hover {
                background: #5a6fd8;
              }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="icon">🚫</div>
              <h1>Bàn đang được sử dụng</h1>
              <div class="message">
                Xin lỗi, <strong>${table.name}</strong> hiện đang có khách sử dụng.
              </div>
              <div class="table-info">
                <div><strong>Trạng thái:</strong> Đang phục vụ</div>
                <div class="time-left">Thời gian còn lại: ${timeLeft} phút</div>
              </div>
              <div class="message">
                Vui lòng chọn bàn khác hoặc liên hệ nhân viên để được hỗ trợ.
              </div>
              <button class="btn" onclick="window.history.back()">← Quay lại</button>
            </div>
          </body>
        </html>
      `);
    }

    // 🆕 CHỈ TẠO KEY MỚI KHI CHƯA CÓ KEY HỢP LỆ
    console.log(`🔑 Tạo key mới cho bàn ${id} (chưa có key hợp lệ)`);

    // 🔒 VÔ HIỆU HÓA TẤT CẢ KEY CŨ TRƯỚC KHI TẠO MỚI (đảm bảo chỉ có 1 key duy nhất)
    await db.executeQuery(`
      UPDATE table_keys
      SET is_valid = 0
      WHERE table_id = @tableId AND is_valid = 1
    `, [
      { name: 'tableId', type: db.sql.Int, value: id }
    ]);
    console.log(`🔒 Đã vô hiệu hóa tất cả key cũ của bàn ${id}`);

    const keyValue = uuidv4();
    const expiresAt = new Date();
    expiresAt.setTime(expiresAt.getTime() + (2 * 60 * 60 * 1000)); // 2 giờ

    // Lưu key vào database
    try {
      console.log('Đang lưu key mới:', {
        tableId: id,
        keyValue: keyValue,
        expiresAt: expiresAt
      });

      await db.executeQuery(
        'INSERT INTO table_keys (table_id, key_value, created_at, expires_at, is_valid) VALUES (@tableId, @keyValue, @createdAt, @expiresAt, @isValid)',
        [
          { name: 'tableId', type: db.sql.Int, value: id },
          { name: 'keyValue', type: db.sql.NVarChar(100), value: keyValue },
          { name: 'createdAt', type: db.sql.DateTime, value: new Date() },
          { name: 'expiresAt', type: db.sql.DateTime, value: expiresAt },
          { name: 'isValid', type: db.sql.Bit, value: true }
        ]
      );
      console.log('✅ Key mới đã được lưu thành công');
    } catch (error) {
      console.error('Lỗi khi lưu key:', error);
      throw error;
    }

    // Cập nhật trạng thái bàn nếu cần
    if (table.status === 'Trống') {
      await db.executeQuery(
        'UPDATE tables SET status = @status WHERE id = @tableId',
        [
          { name: 'tableId', type: db.sql.Int, value: id },
          { name: 'status', type: db.sql.NVarChar(50), value: 'Đang phục vụ' }
        ]
      );
      console.log(`📊 Cập nhật trạng thái bàn ${id}: Trống → Đang phục vụ`);
    }

    // Redirect đến web đặt món với key mới
    const localIP = getLocalIPAddress();
    const orderUrl = `http://${localIP}:${PORT}/order?table_id=${id}&key=${keyValue}`;
    res.redirect(orderUrl);

  } catch (error) {
    console.error('Error generating access key:', error);
    res.status(500).send(`
      <html>
        <head><title>Lỗi hệ thống</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
          <h2>❌ Lỗi hệ thống</h2>
          <p>Vui lòng thử lại sau.</p>
        </body>
      </html>
    `);
  }
});

// Truy cập bàn qua QR code - redirect đến web đặt món
app.get('/api/tables/access/:key', async (req, res) => {
  try {
    const { key } = req.params;
    console.log('Truy cập QR code với key:', key);

    // Kiểm tra key hợp lệ (tạm thời bỏ kiểm tra hết hạn để test)
    const keyResult = await db.executeQuery(`
      SELECT tk.*, t.name as table_name, t.status as table_status
      FROM table_keys tk
      JOIN tables t ON tk.table_id = t.id
      WHERE tk.key_value = @key
        AND tk.is_valid = @isValid
    `, [
      { name: 'key', type: db.sql.NVarChar(100), value: key },
      { name: 'isValid', type: db.sql.Bit, value: true }
    ]);

    console.log('Kết quả tìm key:', keyResult.recordset);

    if (keyResult.recordset.length === 0) {
      return res.status(404).send(`
        <html>
          <head><title>QR Code không hợp lệ</title></head>
          <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h2>❌ QR Code không hợp lệ hoặc đã hết hạn</h2>
            <p>Vui lòng yêu cầu nhân viên tạo QR code mới.</p>
          </body>
        </html>
      `);
    }

    const tableKey = keyResult.recordset[0];

    // Cập nhật trạng thái bàn nếu cần
    if (tableKey.table_status === 'Trống') {
      await db.executeQuery(
        'UPDATE tables SET status = @status WHERE id = @tableId',
        [
          { name: 'tableId', type: db.sql.Int, value: tableKey.table_id },
          { name: 'status', type: db.sql.NVarChar(50), value: 'Đang phục vụ' }
        ]
      );
    }

    // Redirect đến web đặt món với key
    const localIP = getLocalIPAddress();
    const orderUrl = `http://${localIP}:${PORT}/order?table_id=${tableKey.table_id}&key=${key}`;
    res.redirect(orderUrl);

  } catch (error) {
    console.error('Error accessing table with QR code:', error);
    res.status(500).send(`
      <html>
        <head><title>Lỗi hệ thống</title></head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
          <h2>❌ Lỗi hệ thống</h2>
          <p>Vui lòng thử lại sau.</p>
        </body>
      </html>
    `);
  }
});

// Vô hiệu hóa QR code
app.post('/api/tables/invalidate/:key', auth.authenticateToken, async (req, res) => {
  try {
    const { key } = req.params;

    const result = await db.executeQuery(
      'UPDATE table_keys SET is_valid = @isValid OUTPUT INSERTED.* WHERE key_value = @key',
      [
        { name: 'key', type: db.sql.NVarChar(100), value: key },
        { name: 'isValid', type: db.sql.Bit, value: false }
      ]
    );

    if (result.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy QR code' });
    }

    res.json({ message: 'Đã vô hiệu hóa QR code', key: result.recordset[0] });
  } catch (error) {
    console.error('Error invalidating QR code:', error);
    res.status(500).json({ message: 'Lỗi khi vô hiệu hóa QR code' });
  }
});

// Vô hiệu hóa tất cả key của một bàn
app.put('/api/tables/:id/invalidate-key', auth.authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Kiểm tra bàn tồn tại
    const tableResult = await db.executeQuery('SELECT * FROM tables WHERE id = @id', [
      { name: 'id', type: db.sql.Int, value: id }
    ]);

    if (tableResult.recordset.length === 0) {
      return res.status(404).json({ message: 'Không tìm thấy bàn' });
    }

    // Vô hiệu hóa tất cả key của bàn
    const result = await db.executeQuery(
      'UPDATE table_keys SET is_valid = @isValid WHERE table_id = @tableId AND is_valid = @currentValid',
      [
        { name: 'tableId', type: db.sql.Int, value: id },
        { name: 'isValid', type: db.sql.Bit, value: false },
        { name: 'currentValid', type: db.sql.Bit, value: true }
      ]
    );

    // Cập nhật trạng thái bàn về "Trống"
    await db.executeQuery(
      'UPDATE tables SET status = @status WHERE id = @id',
      [
        { name: 'id', type: db.sql.Int, value: id },
        { name: 'status', type: db.sql.NVarChar(50), value: 'Trống' }
      ]
    );

    res.json({
      message: 'Đã vô hiệu hóa tất cả QR code của bàn và đặt lại trạng thái bàn',
      table_id: id,
      invalidated_keys: result.rowsAffected[0]
    });
  } catch (error) {
    console.error('Error invalidating table keys:', error);
    res.status(500).json({ message: 'Lỗi khi vô hiệu hóa key của bàn' });
  }
});

// Payment routes - proxy to payment service
app.post('/api/customer/payments/cash', async (req, res) => {
  try {
    const response = await fetch('http://localhost:3008/api/payments/cash', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(req.body)
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    res.json(data);
  } catch (error) {
    console.error('Error processing cash payment:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.post('/api/customer/payments/momo', async (req, res) => {
  try {
    const response = await fetch('http://localhost:3008/api/payments/momo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(req.body)
    });

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    res.json(data);
  } catch (error) {
    console.error('Error processing MoMo payment:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

app.get('/api/customer/payments/:orderId/status', async (req, res) => {
  try {
    const { orderId } = req.params;
    const response = await fetch(`http://localhost:3008/api/payments/${orderId}/status`);

    const data = await response.json();

    if (!response.ok) {
      return res.status(response.status).json(data);
    }

    res.json(data);
  } catch (error) {
    console.error('Error fetching payment status:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
});

// Xử lý lỗi
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Đã xảy ra lỗi server' });
});

// Đã có setInterval ở trên rồi, xóa cái này

// Khởi động server
server.listen(PORT, '0.0.0.0', () => {
  const localIP = getLocalIPAddress();
  console.log(`Table Service đang chạy tại:`);
  console.log(`  - Local: http://localhost:${PORT}`);
  console.log(`  - Network: http://${localIP}:${PORT}`);
  console.log(`Web đặt món cho thiết bị khác: http://${localIP}:${PORT}/order?table_id=1`);
  console.log(`⏰ Kiểm tra định kỳ trạng thái bàn: mỗi 10 giây`);
});
