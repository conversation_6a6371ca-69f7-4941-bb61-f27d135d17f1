require('dotenv').config();
const { db } = require('./shared');

async function checkTableAndOrders() {
  try {
    console.log('=== KIỂM TRA THÔNG TIN BÀN VÀ ĐỚN HÀNG ===');

    // Kiểm tra thông tin bàn 1
    console.log('\n1. Thông tin bàn 1:');
    const tableResult = await db.executeQuery(
      'SELECT id, name, status, is_buffet FROM tables WHERE id = 1'
    );
    console.log('Bàn 1:', tableResult.recordset[0]);

    // Kiểm tra đơn hàng hiện tại của bàn 1
    console.log('\n2. Đơn hàng hiện tại của bàn 1:');
    const orderResult = await db.executeQuery(`
      SELECT id, table_id, status, payment_status, is_buffet, total, order_time
      FROM orders
      WHERE table_id = 1 AND payment_status != 'paid'
      ORDER BY order_time DESC
    `);

    if (orderResult.recordset.length > 0) {
      console.log('Đơn hàng chưa thanh toán:');
      orderResult.recordset.forEach(order => {
        console.log(`- Đơn #${order.id}: ${order.status}, Buffet: ${order.is_buffet}, Tổng: ${order.total}`);
      });
    } else {
      console.log('Không có đơn hàng chưa thanh toán');
    }

    // Kiểm tra table_keys của bàn 1
    console.log('\n3. Table keys của bàn 1:');
    const keyResult = await db.executeQuery(`
      SELECT key_value, is_valid, created_at, expires_at
      FROM table_keys
      WHERE table_id = 1 AND is_valid = 1
      ORDER BY created_at DESC
    `);

    if (keyResult.recordset.length > 0) {
      console.log('Keys hợp lệ:');
      keyResult.recordset.forEach(key => {
        console.log(`- Key: ${key.key_value}, Expires: ${key.expires_at}`);
      });
    } else {
      console.log('Không có key hợp lệ');
    }

    console.log('\n=== KẾT THÚC KIỂM TRA ===');

  } catch (error) {
    console.error('Error:', error);
  }
}

checkTableAndOrders();
