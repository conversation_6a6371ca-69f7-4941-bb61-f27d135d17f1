require('dotenv').config();
const { db } = require('./shared');

async function checkAndCleanOrders() {
  try {
    console.log('=== KIỂM TRA VÀ DỌN DẸP ĐỚN HÀNG ===');

    // Kiểm tra thông tin bàn 1
    console.log('\n1. Thông tin bàn 1:');
    const tableResult = await db.executeQuery(
      'SELECT id, name, status, is_buffet FROM tables WHERE id = 1'
    );
    console.log('Bàn 1:', tableResult.recordset[0]);

    // Kiểm tra đơn hàng hiện tại của bàn 1
    console.log('\n2. Đơn hàng hiện tại của bàn 1:');
    const orderResult = await db.executeQuery(`
      SELECT id, table_id, status, payment_status, is_buffet, total, order_time
      FROM orders
      WHERE table_id = 1 AND payment_status != 'paid'
      ORDER BY order_time DESC
    `);

    if (orderResult.recordset.length > 0) {
      console.log('Đơn hàng chưa thanh toán:');
      orderResult.recordset.forEach(order => {
        console.log(`- Đơn #${order.id}: ${order.status}, Buffet: ${order.is_buffet}, Key: ${order.table_key}, Tổng: ${order.total}`);
      });

      // Hỏi có muốn xóa đơn hàng cũ không
      console.log('\n🗑️ Đánh dấu tất cả đơn hàng cũ là đã thanh toán để dọn dẹp...');

      for (const order of orderResult.recordset) {
        await db.executeQuery(`
          UPDATE orders
          SET payment_status = 'paid', status = 'Đã thanh toán'
          WHERE id = @orderId
        `, [
          { name: 'orderId', type: db.sql.Int, value: order.id }
        ]);
        console.log(`✅ Đã đánh dấu đơn hàng #${order.id} là đã thanh toán`);
      }

    } else {
      console.log('Không có đơn hàng chưa thanh toán');
    }

    // Kiểm tra table_keys của bàn 1
    console.log('\n3. Table keys của bàn 1:');
    const keyResult = await db.executeQuery(`
      SELECT key_value, is_valid, created_at, expires_at
      FROM table_keys
      WHERE table_id = 1 AND is_valid = 1
      ORDER BY created_at DESC
    `);

    if (keyResult.recordset.length > 0) {
      console.log('Keys hợp lệ:');
      keyResult.recordset.forEach(key => {
        console.log(`- Key: ${key.key_value}, Expires: ${key.expires_at}`);
      });
    } else {
      console.log('Không có key hợp lệ');
    }

    // Kiểm tra lại sau khi dọn dẹp
    console.log('\n4. Kiểm tra lại sau khi dọn dẹp:');
    const finalCheck = await db.executeQuery(`
      SELECT id, table_id, status, payment_status, is_buffet, total, order_time, table_key
      FROM orders
      WHERE table_id = 1 AND payment_status != 'paid'
      ORDER BY order_time DESC
    `);

    if (finalCheck.recordset.length > 0) {
      console.log('Vẫn còn đơn hàng chưa thanh toán:');
      finalCheck.recordset.forEach(order => {
        console.log(`- Đơn #${order.id}: ${order.status}, Buffet: ${order.is_buffet}, Key: ${order.table_key}`);
      });
    } else {
      console.log('✅ Đã dọn dẹp xong! Không còn đơn hàng chưa thanh toán');
    }

    console.log('\n=== HOÀN THÀNH ===');
    console.log('Bây giờ hãy refresh trang web để kiểm tra!');

  } catch (error) {
    console.error('Error:', error);
  }
}

checkAndCleanOrders();
