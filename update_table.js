require('dotenv').config();
const { db } = require('./shared');

async function updateTable() {
  try {
    console.log('Updating table 1 to normal table (is_buffet = false)...');

    const result = await db.executeQuery(
      'UPDATE tables SET is_buffet = 0 WHERE id = 1'
    );

    console.log('Rows affected:', result.rowsAffected[0]);

    console.log('Checking updated table...');
    const checkResult = await db.executeQuery(
      'SELECT id, name, status, is_buffet FROM tables WHERE id = 1'
    );

    console.log('Updated table info:', checkResult.recordset[0]);
    console.log('Update completed successfully!');

  } catch (error) {
    console.error('Error:', error);
  }
}

updateTable();
