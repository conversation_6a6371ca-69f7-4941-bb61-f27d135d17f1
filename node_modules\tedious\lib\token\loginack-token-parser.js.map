{"version": 3, "file": "loginack-token-parser.js", "names": ["_token", "require", "_tdsVersions", "_helpers", "interfaceTypes", "login<PERSON><PERSON><PERSON><PERSON><PERSON>", "buf", "offset", "_options", "token<PERSON><PERSON>th", "value", "readUInt16LE", "length", "NotEnoughDataError", "interfaceNumber", "readUInt8", "interfaceType", "tdsVersionNumber", "readUInt32BE", "tdsVersion", "versions", "progName", "readBVarChar", "major", "minor", "buildNumHi", "buildNumLow", "Result", "LoginAckToken", "interface", "progVersion", "_default", "exports", "default", "module"], "sources": ["../../src/token/loginack-token-parser.ts"], "sourcesContent": ["import { type ParserOptions } from './stream-parser';\n\nimport { LoginAckToken } from './token';\n\nimport { versionsByValue as versions } from '../tds-versions';\nimport { NotEnoughDataError, readBVarChar, readUInt16LE, readUInt32BE, readUInt8, Result } from './helpers';\n\nconst interfaceTypes: { [key: number]: string } = {\n  0: 'SQL_DFLT',\n  1: 'SQL_TSQL'\n};\n\nfunction loginAckParser(buf: Buffer, offset: number, _options: ParserOptions): Result<LoginAckToken> {\n  // length\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < tokenLength + offset) {\n    throw new NotEnoughDataError(tokenLength + offset);\n  }\n\n  let interfaceNumber;\n  ({ offset, value: interfaceNumber } = readUInt8(buf, offset));\n\n  const interfaceType = interfaceTypes[interfaceNumber];\n\n  let tdsVersionNumber;\n  ({ offset, value: tdsVersionNumber } = readUInt32BE(buf, offset));\n\n  const tdsVersion = versions[tdsVersionNumber];\n\n  let progName;\n  ({ offset, value: progName } = readBVarChar(buf, offset));\n\n  let major;\n  ({ offset, value: major } = readUInt8(buf, offset));\n\n  let minor;\n  ({ offset, value: minor } = readUInt8(buf, offset));\n\n  let buildNumHi;\n  ({ offset, value: buildNumHi } = readUInt8(buf, offset));\n\n  let buildNumLow;\n  ({ offset, value: buildNumLow } = readUInt8(buf, offset));\n\n  return new Result(new LoginAckToken({\n    interface: interfaceType,\n    tdsVersion: tdsVersion,\n    progName: progName,\n    progVersion: {\n      major: major,\n      minor: minor,\n      buildNumHi: buildNumHi,\n      buildNumLow: buildNumLow\n    }\n  }), offset);\n}\n\nexport default loginAckParser;\nmodule.exports = loginAckParser;\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AAEA,MAAMG,cAAyC,GAAG;EAChD,CAAC,EAAE,UAAU;EACb,CAAC,EAAE;AACL,CAAC;AAED,SAASC,cAAcA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAuB,EAAyB;EACnG;EACA,IAAIC,WAAW;EACf,CAAC;IAAEF,MAAM;IAAEG,KAAK,EAAED;EAAY,CAAC,GAAG,IAAAE,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAID,GAAG,CAACM,MAAM,GAAGH,WAAW,GAAGF,MAAM,EAAE;IACrC,MAAM,IAAIM,2BAAkB,CAACJ,WAAW,GAAGF,MAAM,CAAC;EACpD;EAEA,IAAIO,eAAe;EACnB,CAAC;IAAEP,MAAM;IAAEG,KAAK,EAAEI;EAAgB,CAAC,GAAG,IAAAC,kBAAS,EAACT,GAAG,EAAEC,MAAM,CAAC;EAE5D,MAAMS,aAAa,GAAGZ,cAAc,CAACU,eAAe,CAAC;EAErD,IAAIG,gBAAgB;EACpB,CAAC;IAAEV,MAAM;IAAEG,KAAK,EAAEO;EAAiB,CAAC,GAAG,IAAAC,qBAAY,EAACZ,GAAG,EAAEC,MAAM,CAAC;EAEhE,MAAMY,UAAU,GAAGC,4BAAQ,CAACH,gBAAgB,CAAC;EAE7C,IAAII,QAAQ;EACZ,CAAC;IAAEd,MAAM;IAAEG,KAAK,EAAEW;EAAS,CAAC,GAAG,IAAAC,qBAAY,EAAChB,GAAG,EAAEC,MAAM,CAAC;EAExD,IAAIgB,KAAK;EACT,CAAC;IAAEhB,MAAM;IAAEG,KAAK,EAAEa;EAAM,CAAC,GAAG,IAAAR,kBAAS,EAACT,GAAG,EAAEC,MAAM,CAAC;EAElD,IAAIiB,KAAK;EACT,CAAC;IAAEjB,MAAM;IAAEG,KAAK,EAAEc;EAAM,CAAC,GAAG,IAAAT,kBAAS,EAACT,GAAG,EAAEC,MAAM,CAAC;EAElD,IAAIkB,UAAU;EACd,CAAC;IAAElB,MAAM;IAAEG,KAAK,EAAEe;EAAW,CAAC,GAAG,IAAAV,kBAAS,EAACT,GAAG,EAAEC,MAAM,CAAC;EAEvD,IAAImB,WAAW;EACf,CAAC;IAAEnB,MAAM;IAAEG,KAAK,EAAEgB;EAAY,CAAC,GAAG,IAAAX,kBAAS,EAACT,GAAG,EAAEC,MAAM,CAAC;EAExD,OAAO,IAAIoB,eAAM,CAAC,IAAIC,oBAAa,CAAC;IAClCC,SAAS,EAAEb,aAAa;IACxBG,UAAU,EAAEA,UAAU;IACtBE,QAAQ,EAAEA,QAAQ;IAClBS,WAAW,EAAE;MACXP,KAAK,EAAEA,KAAK;MACZC,KAAK,EAAEA,KAAK;MACZC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA;IACf;EACF,CAAC,CAAC,EAAEnB,MAAM,CAAC;AACb;AAAC,IAAAwB,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEc5B,cAAc;AAC7B6B,MAAM,CAACF,OAAO,GAAG3B,cAAc"}