{"version": 3, "file": "returnstatus-token-parser.js", "names": ["_helpers", "require", "_token", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buf", "offset", "_options", "value", "readInt32LE", "Result", "ReturnStatusToken", "_default", "exports", "default", "module"], "sources": ["../../src/token/returnstatus-token-parser.ts"], "sourcesContent": ["// s2.2.7.16\nimport { readInt32LE, Result } from './helpers';\nimport { type ParserOptions } from './stream-parser';\n\nimport { ReturnStatusToken } from './token';\n\nfunction returnStatusParser(buf: Buffer, offset: number, _options: ParserOptions): Result<ReturnStatusToken> {\n  let value;\n  ({ value, offset } = readInt32LE(buf, offset));\n  return new Result(new ReturnStatusToken(value), offset);\n}\n\nexport default returnStatusParser;\nmodule.exports = returnStatusParser;\n"], "mappings": ";;;;;;AACA,IAAAA,QAAA,GAAAC,OAAA;AAGA,IAAAC,MAAA,GAAAD,OAAA;AAJA;;AAMA,SAASE,kBAAkBA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAuB,EAA6B;EAC3G,IAAIC,KAAK;EACT,CAAC;IAAEA,KAAK;IAAEF;EAAO,CAAC,GAAG,IAAAG,oBAAW,EAACJ,GAAG,EAAEC,MAAM,CAAC;EAC7C,OAAO,IAAII,eAAM,CAAC,IAAIC,wBAAiB,CAACH,KAAK,CAAC,EAAEF,MAAM,CAAC;AACzD;AAAC,IAAAM,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcV,kBAAkB;AACjCW,MAAM,CAACF,OAAO,GAAGT,kBAAkB"}