{"author": {"name": "<PERSON><PERSON>", "url": "https://patriksimek.cz"}, "name": "mssql", "description": "Microsoft SQL Server client for Node.js.", "keywords": ["database", "mssql", "sql", "server", "msnodesql", "sqlserver", "tds", "node-tds", "tedious", "node-sqlserver", "sqlserver", "msnodesqlv8", "azure", "node-mssql"], "version": "11.0.1", "main": "index.js", "type": "commonjs", "repository": "github:tediousjs/node-mssql", "license": "MIT", "dependencies": {"@tediousjs/connection-string": "^0.5.0", "commander": "^11.0.0", "debug": "^4.3.3", "rfdc": "^1.3.0", "tarn": "^3.0.2", "tedious": "^18.2.1"}, "devDependencies": {"@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@semantic-release/commit-analyzer": "^11.1.0", "@semantic-release/github": "^9.2.6", "@semantic-release/npm": "^11.0.3", "@semantic-release/release-notes-generator": "^12.1.0", "mocha": "^10.0.0", "semantic-release": "^22.0.12", "standard": "^17.0.0"}, "engines": {"node": ">=18"}, "files": ["lib/", "bin/", "tedious.js", "msnodesqlv8.js"], "scripts": {"commitlint": "commitlint --from origin/master --to HEAD", "test": "npm run lint && npm run test-unit", "lint": "standard", "test-unit": "mocha --exit -t 15000 test/common/unit.js", "test-tedious": "mocha --exit -t 15000 test/tedious", "test-msnodesqlv8": "mocha --exit -t 30000 test/msnodesqlv8", "test-cli": "mocha --exit -t 15000 test/common/cli.js"}, "bin": {"mssql": "./bin/mssql"}}