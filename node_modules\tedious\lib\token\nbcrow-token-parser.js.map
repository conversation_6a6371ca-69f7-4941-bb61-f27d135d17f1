{"version": 3, "file": "nbcrow-token-parser.js", "names": ["_token", "require", "iconv", "_interopRequireWildcard", "_valueParser", "_helpers", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "nbc<PERSON>ow<PERSON><PERSON><PERSON>", "parser", "colMetadata", "columns", "bitmap", "bitmapByteLength", "Math", "ceil", "length", "buffer", "position", "waitForChunk", "bytes", "slice", "len", "byte", "push", "metadata", "value", "isPLPStream", "chunks", "readPLPStream", "type", "name", "<PERSON><PERSON><PERSON>", "concat", "toString", "decode", "collation", "codepage", "result", "readValue", "options", "err", "NotEnoughDataError", "offset", "useColumnNames", "columnsMap", "create", "for<PERSON>ach", "column", "colName", "NBCRowToken", "_default", "exports", "module"], "sources": ["../../src/token/nbcrow-token-parser.ts"], "sourcesContent": ["// s2.2.7.13 (introduced in TDS 7.3.B)\n\nimport Parser from './stream-parser';\nimport { type ColumnMetadata } from './colmetadata-token-parser';\n\nimport { NBCRowToken } from './token';\nimport * as iconv from 'iconv-lite';\n\nimport { isPLPStream, readPLPStream, readValue } from '../value-parser';\nimport { NotEnoughDataError } from './helpers';\n\ninterface Column {\n  value: unknown;\n  metadata: ColumnMetadata;\n}\n\nasync function nbcRowParser(parser: Parser): Promise<NBCRowToken> {\n  const colMetadata = parser.colMetadata;\n  const columns: Column[] = [];\n  const bitmap: boolean[] = [];\n  const bitmapByteLength = Math.ceil(colMetadata.length / 8);\n\n  while (parser.buffer.length - parser.position < bitmapByteLength) {\n    await parser.waitForChunk();\n  }\n\n  const bytes = parser.buffer.slice(parser.position, parser.position + bitmapByteLength);\n  parser.position += bitmapByteLength;\n\n  for (let i = 0, len = bytes.length; i < len; i++) {\n    const byte = bytes[i];\n\n    bitmap.push(byte & 0b1 ? true : false);\n    bitmap.push(byte & 0b10 ? true : false);\n    bitmap.push(byte & 0b100 ? true : false);\n    bitmap.push(byte & 0b1000 ? true : false);\n    bitmap.push(byte & 0b10000 ? true : false);\n    bitmap.push(byte & 0b100000 ? true : false);\n    bitmap.push(byte & 0b1000000 ? true : false);\n    bitmap.push(byte & 0b10000000 ? true : false);\n  }\n\n  for (let i = 0; i < colMetadata.length; i++) {\n    const metadata = colMetadata[i];\n    if (bitmap[i]) {\n      columns.push({ value: null, metadata });\n      continue;\n    }\n\n    while (true) {\n      if (isPLPStream(metadata)) {\n        const chunks = await readPLPStream(parser);\n\n        if (chunks === null) {\n          columns.push({ value: chunks, metadata });\n        } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {\n          columns.push({ value: Buffer.concat(chunks).toString('ucs2'), metadata });\n        } else if (metadata.type.name === 'VarChar') {\n          columns.push({ value: iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8'), metadata });\n        } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {\n          columns.push({ value: Buffer.concat(chunks), metadata });\n        }\n      } else {\n        let result;\n        try {\n          result = readValue(parser.buffer, parser.position, metadata, parser.options);\n        } catch (err) {\n          if (err instanceof NotEnoughDataError) {\n            await parser.waitForChunk();\n            continue;\n          }\n\n          throw err;\n        }\n\n        parser.position = result.offset;\n        columns.push({ value: result.value, metadata });\n      }\n\n      break;\n    }\n  }\n\n  if (parser.options.useColumnNames) {\n    const columnsMap: { [key: string]: Column } = Object.create(null);\n\n    columns.forEach((column) => {\n      const colName = column.metadata.colName;\n      if (columnsMap[colName] == null) {\n        columnsMap[colName] = column;\n      }\n    });\n\n    return new NBCRowToken(columnsMap);\n  } else {\n    return new NBCRowToken(columns);\n  }\n}\n\n\nexport default nbcRowParser;\nmodule.exports = nbcRowParser;\n"], "mappings": ";;;;;;AAKA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAC,uBAAA,CAAAF,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AACA,IAAAI,QAAA,GAAAJ,OAAA;AAA+C,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAT/C;;AAgBA,eAAeY,YAAYA,CAACC,MAAc,EAAwB;EAChE,MAAMC,WAAW,GAAGD,MAAM,CAACC,WAAW;EACtC,MAAMC,OAAiB,GAAG,EAAE;EAC5B,MAAMC,MAAiB,GAAG,EAAE;EAC5B,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,IAAI,CAACL,WAAW,CAACM,MAAM,GAAG,CAAC,CAAC;EAE1D,OAAOP,MAAM,CAACQ,MAAM,CAACD,MAAM,GAAGP,MAAM,CAACS,QAAQ,GAAGL,gBAAgB,EAAE;IAChE,MAAMJ,MAAM,CAACU,YAAY,CAAC,CAAC;EAC7B;EAEA,MAAMC,KAAK,GAAGX,MAAM,CAACQ,MAAM,CAACI,KAAK,CAACZ,MAAM,CAACS,QAAQ,EAAET,MAAM,CAACS,QAAQ,GAAGL,gBAAgB,CAAC;EACtFJ,MAAM,CAACS,QAAQ,IAAIL,gBAAgB;EAEnC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEgB,GAAG,GAAGF,KAAK,CAACJ,MAAM,EAAEV,CAAC,GAAGgB,GAAG,EAAEhB,CAAC,EAAE,EAAE;IAChD,MAAMiB,IAAI,GAAGH,KAAK,CAACd,CAAC,CAAC;IAErBM,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,KAAK,CAAC;IACtCX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,CAAC;IACvCX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;IACxCX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,MAAM,GAAG,IAAI,GAAG,KAAK,CAAC;IACzCX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,OAAO,GAAG,IAAI,GAAG,KAAK,CAAC;IAC1CX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,QAAQ,GAAG,IAAI,GAAG,KAAK,CAAC;IAC3CX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,SAAS,GAAG,IAAI,GAAG,KAAK,CAAC;IAC5CX,MAAM,CAACY,IAAI,CAACD,IAAI,GAAG,UAAU,GAAG,IAAI,GAAG,KAAK,CAAC;EAC/C;EAEA,KAAK,IAAIjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,WAAW,CAACM,MAAM,EAAEV,CAAC,EAAE,EAAE;IAC3C,MAAMmB,QAAQ,GAAGf,WAAW,CAACJ,CAAC,CAAC;IAC/B,IAAIM,MAAM,CAACN,CAAC,CAAC,EAAE;MACbK,OAAO,CAACa,IAAI,CAAC;QAAEE,KAAK,EAAE,IAAI;QAAED;MAAS,CAAC,CAAC;MACvC;IACF;IAEA,OAAO,IAAI,EAAE;MACX,IAAI,IAAAE,wBAAW,EAACF,QAAQ,CAAC,EAAE;QACzB,MAAMG,MAAM,GAAG,MAAM,IAAAC,0BAAa,EAACpB,MAAM,CAAC;QAE1C,IAAImB,MAAM,KAAK,IAAI,EAAE;UACnBjB,OAAO,CAACa,IAAI,CAAC;YAAEE,KAAK,EAAEE,MAAM;YAAEH;UAAS,CAAC,CAAC;QAC3C,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,UAAU,IAAIN,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC5EpB,OAAO,CAACa,IAAI,CAAC;YAAEE,KAAK,EAAEM,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,MAAM,CAAC;YAAET;UAAS,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;UAC3CpB,OAAO,CAACa,IAAI,CAAC;YAAEE,KAAK,EAAE3C,KAAK,CAACoD,MAAM,CAACH,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,EAAEH,QAAQ,CAACW,SAAS,EAAEC,QAAQ,IAAI,MAAM,CAAC;YAAEZ;UAAS,CAAC,CAAC;QAChH,CAAC,MAAM,IAAIA,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,WAAW,IAAIN,QAAQ,CAACK,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;UAC7EpB,OAAO,CAACa,IAAI,CAAC;YAAEE,KAAK,EAAEM,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC;YAAEH;UAAS,CAAC,CAAC;QAC1D;MACF,CAAC,MAAM;QACL,IAAIa,MAAM;QACV,IAAI;UACFA,MAAM,GAAG,IAAAC,sBAAS,EAAC9B,MAAM,CAACQ,MAAM,EAAER,MAAM,CAACS,QAAQ,EAAEO,QAAQ,EAAEhB,MAAM,CAAC+B,OAAO,CAAC;QAC9E,CAAC,CAAC,OAAOC,GAAG,EAAE;UACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;YACrC,MAAMjC,MAAM,CAACU,YAAY,CAAC,CAAC;YAC3B;UACF;UAEA,MAAMsB,GAAG;QACX;QAEAhC,MAAM,CAACS,QAAQ,GAAGoB,MAAM,CAACK,MAAM;QAC/BhC,OAAO,CAACa,IAAI,CAAC;UAAEE,KAAK,EAAEY,MAAM,CAACZ,KAAK;UAAED;QAAS,CAAC,CAAC;MACjD;MAEA;IACF;EACF;EAEA,IAAIhB,MAAM,CAAC+B,OAAO,CAACI,cAAc,EAAE;IACjC,MAAMC,UAAqC,GAAG9C,MAAM,CAAC+C,MAAM,CAAC,IAAI,CAAC;IAEjEnC,OAAO,CAACoC,OAAO,CAAEC,MAAM,IAAK;MAC1B,MAAMC,OAAO,GAAGD,MAAM,CAACvB,QAAQ,CAACwB,OAAO;MACvC,IAAIJ,UAAU,CAACI,OAAO,CAAC,IAAI,IAAI,EAAE;QAC/BJ,UAAU,CAACI,OAAO,CAAC,GAAGD,MAAM;MAC9B;IACF,CAAC,CAAC;IAEF,OAAO,IAAIE,kBAAW,CAACL,UAAU,CAAC;EACpC,CAAC,MAAM;IACL,OAAO,IAAIK,kBAAW,CAACvC,OAAO,CAAC;EACjC;AACF;AAAC,IAAAwC,QAAA,GAAAC,OAAA,CAAA3D,OAAA,GAGce,YAAY;AAC3B6C,MAAM,CAACD,OAAO,GAAG5C,YAAY"}