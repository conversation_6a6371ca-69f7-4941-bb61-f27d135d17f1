{"version": 3, "file": "returnvalue-token-parser.js", "names": ["_token", "require", "_metadataParser", "_valueParser", "_helpers", "iconv", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "<PERSON><PERSON><PERSON><PERSON>", "parser", "paramName", "paramOrdinal", "metadata", "buf", "buffer", "offset", "position", "value", "readUInt16LE", "readBVarChar", "readUInt8", "readMetadata", "options", "char<PERSON>t", "slice", "err", "NotEnoughDataError", "waitForChunk", "isPLPStream", "chunks", "readPLPStream", "type", "name", "<PERSON><PERSON><PERSON>", "concat", "toString", "decode", "collation", "codepage", "readValue", "ReturnValueToken", "_default", "exports", "module"], "sources": ["../../src/token/returnvalue-token-parser.ts"], "sourcesContent": ["// s2.2.7.16\n\nimport Parser from './stream-parser';\n\nimport { ReturnValueToken } from './token';\n\nimport { readMetadata } from '../metadata-parser';\nimport { isPLPStream, readPLPStream, readValue } from '../value-parser';\nimport { NotEnoughDataError, readBVarChar, readUInt16LE, readUInt8 } from './helpers';\nimport * as iconv from 'iconv-lite';\n\nasync function returnParser(parser: Parser): Promise<ReturnValueToken> {\n  let paramName;\n  let paramOrdinal;\n  let metadata;\n\n  while (true) {\n    const buf = parser.buffer;\n    let offset = parser.position;\n\n    try {\n      ({ offset, value: paramOrdinal } = readUInt16LE(buf, offset));\n      ({ offset, value: paramName } = readBVarChar(buf, offset));\n      // status\n      ({ offset } = readUInt8(buf, offset));\n      ({ offset, value: metadata } = readMetadata(buf, offset, parser.options));\n\n      if (paramName.charAt(0) === '@') {\n        paramName = paramName.slice(1);\n      }\n    } catch (err) {\n      if (err instanceof NotEnoughDataError) {\n        await parser.waitForChunk();\n        continue;\n      }\n\n      throw err;\n    }\n\n    parser.position = offset;\n    break;\n  }\n\n  let value;\n  while (true) {\n    const buf = parser.buffer;\n    let offset = parser.position;\n\n    if (isPLPStream(metadata)) {\n      const chunks = await readPLPStream(parser);\n\n      if (chunks === null) {\n        value = chunks;\n      } else if (metadata.type.name === 'NVarChar' || metadata.type.name === 'Xml') {\n        value = Buffer.concat(chunks).toString('ucs2');\n      } else if (metadata.type.name === 'VarChar') {\n        value = iconv.decode(Buffer.concat(chunks), metadata.collation?.codepage ?? 'utf8');\n      } else if (metadata.type.name === 'VarBinary' || metadata.type.name === 'UDT') {\n        value = Buffer.concat(chunks);\n      }\n    } else {\n      try {\n        ({ value, offset } = readValue(buf, offset, metadata, parser.options));\n      } catch (err) {\n        if (err instanceof NotEnoughDataError) {\n          await parser.waitForChunk();\n          continue;\n        }\n\n        throw err;\n      }\n\n      parser.position = offset;\n    }\n\n    break;\n  }\n\n  return new ReturnValueToken({\n    paramOrdinal: paramOrdinal,\n    paramName: paramName,\n    metadata: metadata,\n    value: value\n  });\n}\n\nexport default returnParser;\nmodule.exports = returnParser;\n"], "mappings": ";;;;;;AAIA,IAAAA,MAAA,GAAAC,OAAA;AAEA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AACA,IAAAI,KAAA,GAAAC,uBAAA,CAAAL,OAAA;AAAoC,SAAAM,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AATpC;;AAWA,eAAeY,YAAYA,CAACC,MAAc,EAA6B;EACrE,IAAIC,SAAS;EACb,IAAIC,YAAY;EAChB,IAAIC,QAAQ;EAEZ,OAAO,IAAI,EAAE;IACX,MAAMC,GAAG,GAAGJ,MAAM,CAACK,MAAM;IACzB,IAAIC,MAAM,GAAGN,MAAM,CAACO,QAAQ;IAE5B,IAAI;MACF,CAAC;QAAED,MAAM;QAAEE,KAAK,EAAEN;MAAa,CAAC,GAAG,IAAAO,qBAAY,EAACL,GAAG,EAAEE,MAAM,CAAC;MAC5D,CAAC;QAAEA,MAAM;QAAEE,KAAK,EAAEP;MAAU,CAAC,GAAG,IAAAS,qBAAY,EAACN,GAAG,EAAEE,MAAM,CAAC;MACzD;MACA,CAAC;QAAEA;MAAO,CAAC,GAAG,IAAAK,kBAAS,EAACP,GAAG,EAAEE,MAAM,CAAC;MACpC,CAAC;QAAEA,MAAM;QAAEE,KAAK,EAAEL;MAAS,CAAC,GAAG,IAAAS,4BAAY,EAACR,GAAG,EAAEE,MAAM,EAAEN,MAAM,CAACa,OAAO,CAAC;MAExE,IAAIZ,SAAS,CAACa,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAC/Bb,SAAS,GAAGA,SAAS,CAACc,KAAK,CAAC,CAAC,CAAC;MAChC;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;QACrC,MAAMjB,MAAM,CAACkB,YAAY,CAAC,CAAC;QAC3B;MACF;MAEA,MAAMF,GAAG;IACX;IAEAhB,MAAM,CAACO,QAAQ,GAAGD,MAAM;IACxB;EACF;EAEA,IAAIE,KAAK;EACT,OAAO,IAAI,EAAE;IACX,MAAMJ,GAAG,GAAGJ,MAAM,CAACK,MAAM;IACzB,IAAIC,MAAM,GAAGN,MAAM,CAACO,QAAQ;IAE5B,IAAI,IAAAY,wBAAW,EAAChB,QAAQ,CAAC,EAAE;MACzB,MAAMiB,MAAM,GAAG,MAAM,IAAAC,0BAAa,EAACrB,MAAM,CAAC;MAE1C,IAAIoB,MAAM,KAAK,IAAI,EAAE;QACnBZ,KAAK,GAAGY,MAAM;MAChB,CAAC,MAAM,IAAIjB,QAAQ,CAACmB,IAAI,CAACC,IAAI,KAAK,UAAU,IAAIpB,QAAQ,CAACmB,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;QAC5Ef,KAAK,GAAGgB,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,CAACM,QAAQ,CAAC,MAAM,CAAC;MAChD,CAAC,MAAM,IAAIvB,QAAQ,CAACmB,IAAI,CAACC,IAAI,KAAK,SAAS,EAAE;QAC3Cf,KAAK,GAAGhC,KAAK,CAACmD,MAAM,CAACH,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC,EAAEjB,QAAQ,CAACyB,SAAS,EAAEC,QAAQ,IAAI,MAAM,CAAC;MACrF,CAAC,MAAM,IAAI1B,QAAQ,CAACmB,IAAI,CAACC,IAAI,KAAK,WAAW,IAAIpB,QAAQ,CAACmB,IAAI,CAACC,IAAI,KAAK,KAAK,EAAE;QAC7Ef,KAAK,GAAGgB,MAAM,CAACC,MAAM,CAACL,MAAM,CAAC;MAC/B;IACF,CAAC,MAAM;MACL,IAAI;QACF,CAAC;UAAEZ,KAAK;UAAEF;QAAO,CAAC,GAAG,IAAAwB,sBAAS,EAAC1B,GAAG,EAAEE,MAAM,EAAEH,QAAQ,EAAEH,MAAM,CAACa,OAAO,CAAC;MACvE,CAAC,CAAC,OAAOG,GAAG,EAAE;QACZ,IAAIA,GAAG,YAAYC,2BAAkB,EAAE;UACrC,MAAMjB,MAAM,CAACkB,YAAY,CAAC,CAAC;UAC3B;QACF;QAEA,MAAMF,GAAG;MACX;MAEAhB,MAAM,CAACO,QAAQ,GAAGD,MAAM;IAC1B;IAEA;EACF;EAEA,OAAO,IAAIyB,uBAAgB,CAAC;IAC1B7B,YAAY,EAAEA,YAAY;IAC1BD,SAAS,EAAEA,SAAS;IACpBE,QAAQ,EAAEA,QAAQ;IAClBK,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AAAC,IAAAwB,QAAA,GAAAC,OAAA,CAAAjD,OAAA,GAEce,YAAY;AAC3BmC,MAAM,CAACD,OAAO,GAAGlC,YAAY"}