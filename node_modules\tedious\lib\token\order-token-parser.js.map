{"version": 3, "file": "order-token-parser.js", "names": ["_token", "require", "_helpers", "<PERSON><PERSON><PERSON><PERSON>", "buf", "offset", "_options", "token<PERSON><PERSON>th", "value", "readUInt16LE", "length", "NotEnoughDataError", "orderColumns", "i", "column", "push", "Result", "OrderToken", "_default", "exports", "default", "module"], "sources": ["../../src/token/order-token-parser.ts"], "sourcesContent": ["// s2.2.7.14\nimport { type ParserOptions } from './stream-parser';\n\nimport { OrderToken } from './token';\nimport { NotEnoughDataError, readUInt16LE, Result } from './helpers';\n\nfunction orderParser(buf: Buffer, offset: number, _options: ParserOptions): Result<OrderToken> {\n  // length\n  let tokenLength;\n  ({ offset, value: tokenLength } = readUInt16LE(buf, offset));\n\n  if (buf.length < offset + tokenLength) {\n    throw new NotEnoughDataError(offset + tokenLength);\n  }\n\n  const orderColumns: number[] = [];\n\n  for (let i = 0; i < tokenLength; i += 2) {\n    let column;\n    ({ offset, value: column } = readUInt16LE(buf, offset));\n\n    orderColumns.push(column);\n  }\n\n  return new Result(new OrderToken(orderColumns), offset);\n}\n\nexport default orderParser;\nmodule.exports = orderParser;\n"], "mappings": ";;;;;;AAGA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,QAAA,GAAAD,OAAA;AAJA;;AAMA,SAASE,WAAWA,CAACC,GAAW,EAAEC,MAAc,EAAEC,QAAuB,EAAsB;EAC7F;EACA,IAAIC,WAAW;EACf,CAAC;IAAEF,MAAM;IAAEG,KAAK,EAAED;EAAY,CAAC,GAAG,IAAAE,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;EAE3D,IAAID,GAAG,CAACM,MAAM,GAAGL,MAAM,GAAGE,WAAW,EAAE;IACrC,MAAM,IAAII,2BAAkB,CAACN,MAAM,GAAGE,WAAW,CAAC;EACpD;EAEA,MAAMK,YAAsB,GAAG,EAAE;EAEjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,EAAEM,CAAC,IAAI,CAAC,EAAE;IACvC,IAAIC,MAAM;IACV,CAAC;MAAET,MAAM;MAAEG,KAAK,EAAEM;IAAO,CAAC,GAAG,IAAAL,qBAAY,EAACL,GAAG,EAAEC,MAAM,CAAC;IAEtDO,YAAY,CAACG,IAAI,CAACD,MAAM,CAAC;EAC3B;EAEA,OAAO,IAAIE,eAAM,CAAC,IAAIC,iBAAU,CAACL,YAAY,CAAC,EAAEP,MAAM,CAAC;AACzD;AAAC,IAAAa,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEcjB,WAAW;AAC1BkB,MAAM,CAACF,OAAO,GAAGhB,WAAW"}